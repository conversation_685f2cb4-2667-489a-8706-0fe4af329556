{"name": "my-project", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --debug", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp": "node node_modules/@dcloudio/uni-quickapp/bin/serve.js"}, "dependencies": {"@dcloudio/uni-app": "2.0.2-4060620250520001", "@dcloudio/uni-app-plus": "2.0.2-4060620250520001", "@dcloudio/uni-h5": "2.0.2-4060620250520001", "@dcloudio/uni-helper-json": "^1.0.13", "@dcloudio/uni-i18n": "2.0.2-4060620250520001", "@dcloudio/uni-mp-360": "2.0.2-4060620250520001", "@dcloudio/uni-mp-alipay": "2.0.2-4060620250520001", "@dcloudio/uni-mp-baidu": "2.0.2-4060620250520001", "@dcloudio/uni-mp-harmony": "2.0.2-4060620250520001", "@dcloudio/uni-mp-jd": "2.0.2-4060620250520001", "@dcloudio/uni-mp-kuaishou": "2.0.2-4060620250520001", "@dcloudio/uni-mp-lark": "2.0.2-4060620250520001", "@dcloudio/uni-mp-qq": "2.0.2-4060620250520001", "@dcloudio/uni-mp-toutiao": "2.0.2-4060620250520001", "@dcloudio/uni-mp-vue": "2.0.2-4060620250520001", "@dcloudio/uni-mp-weixin": "2.0.2-4060620250520001", "@dcloudio/uni-mp-xhs": "2.0.2-4060620250520001", "@dcloudio/uni-quickapp-native": "2.0.2-4060620250520001", "@dcloudio/uni-quickapp-webview": "2.0.2-4060620250520001", "@dcloudio/uni-stacktracey": "2.0.2-4060620250520001", "@dcloudio/uni-stat": "2.0.2-4060620250520001", "@vue/shared": "3.5.17", "alife-logger": "^1.8.30", "core-js": "^3.9.1", "element-china-area-data": "^4.1.2", "flyio": "0.6.14", "hina-cloud-js-sdk": "^4.0.2", "html2canvas": "^1.4.1", "js-sha256": "^0.9.0", "jsmind": "^0.4.6", "jweixin-module": "^1.6.0", "luo-vue-aliplayer": "^1.0.18", "moment": "^2.29.1", "plus-websocket": "^1.0.5", "regenerator-runtime": "^0.12.1", "vue": "^2.6.12", "vuex": "3.6.2", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@dcloudio/uni-automator": "2.0.2-4060620250520001", "@dcloudio/uni-cli-i18n": "2.0.2-4060620250520001", "@dcloudio/uni-cli-shared": "2.0.2-4060620250520001", "@dcloudio/uni-migration": "2.0.2-4060620250520001", "@dcloudio/uni-template-compiler": "2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-hbuilderx": "2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-uni": "2.0.2-4060620250520001", "@dcloudio/vue-cli-plugin-uni-optimize": "2.0.2-4060620250520001", "@dcloudio/webpack-uni-mp-loader": "2.0.2-4060620250520001", "@dcloudio/webpack-uni-pages-loader": "2.0.2-4060620250520001", "@types/html5plus": "^1.0.1", "@types/uni-app": "^1.4.3", "@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-service": "~4.3.0", "babel-plugin-import": "1.13.8", "cross-env": "7.0.3", "jest": "25.5.4", "less": "^3.13.1", "less-loader": "^5.0.0", "mini-types": "^0.1.5", "miniprogram-api-typings": "^2.12.0", "postcss-comment": "^2.0.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "source-map": "^0.7.4", "vue-template-compiler": "^2.6.12"}, "browserslist": ["Android >= 8", "ios >= 10"], "uni-app": {"scripts": {}}}