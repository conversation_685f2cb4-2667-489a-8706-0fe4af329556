<template>
  <div class="error-page">
    <div class="status-box">
      <img src="../../../../public/active/失败.png" alt="">
      <div class="error-text">{{ errorText }}</div>
      <span v-if="sign_type === 'sign'">请联系您的班主任</span>
    </div>
    <div class="qrcode">
      <div class="qrcode-bg">
        <img :src="qrcodeSrc" alt="">
      </div>
      <div class="tips">长按扫描二维码，添加班主任老师</div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      errorText: "提交成功",
      qrcodeSrc: "",
      sign_type: ""
    }
  },
  computed: {},
  methods: {},
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    this.errorText = option.text;
    this.qrcodeSrc = option.qrcodeSrc;
    this.sign_type = option.sign_type;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
} 
</script>

<style lang="scss" scoped>
.error-page {
  width: 100%;
  height: 90vh;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  padding-top: 200rpx;
  .status-box {
    text-align: center;
    padding-bottom: 120rpx;
    img {
      width: 240rpx;
    }
    .error-text {
      color: #b0b0b0;
      margin-top: 20rpx;
      font-size: 45rpx;
      font-weight: 600;
    }
    span {
      color: #b0b0b0;
      font-size: 24rpx;
      margin-top: 10rpx;
    }
  }
  .qrcode {
    text-align: center;
    .qrcode-bg {
      background: #f7cec4;
      border-radius: 10rpx;
      width: 380rpx;
      height: 380rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      img {
        width: calc(100% - 30rpx);
        height: calc(100% - 30rpx);
        padding: 30rpx;
      }
    }
    .tips {
      margin-top: 20rpx;
      font-size: 28rpx;
    }
  }
}
</style>