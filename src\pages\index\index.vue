<template>
  <view class="content">
    <image
      class="logo"
      mode="widthFix"
      v-for="(item, index) in info.image_lists"
      :key="index"
      :src="item"
    />
    <!-- <image class="logo" mode="widthFix" src="/static/index/1.png" />
    <image class="logo" mode="widthFix" src="/static/index/2.png" />
    <image class="logo" mode="widthFix" src="/static/index/3.png" />
    <image class="logo" mode="widthFix" src="/static/index/4.png" />
    <image class="logo" mode="widthFix" src="/static/index/5.png" />-->
    <view class="bottom_shop">
      <view style="color: #ff6644; margin: 0 40rpx">
        ¥
        <text class="price">{{
          info.price ? (info.price / 100).toFixed(2) : 0
        }}</text>
      </view>
      <view class="shop_btn" @click="goOrder">{{
        info.price == 0 ? "限时免费报名" : "立即购买"
      }}</view>
    </view>
  </view>
</template>

<script>
!(function(g, d, t, e, v, n, s) {
  if (g.gdt) return;
  v = g.gdt = function() {
    v.tk ? v.tk.apply(v, arguments) : v.queue.push(arguments);
  };
  v.sv = "1.0";
  v.bt = 0;
  v.queue = [];
  n = d.createElement(t);
  n.async = !0;
  n.src = e;
  s = d.getElementsByTagName(t)[0];
  s.parentNode.insertBefore(n, s);
})(
  window,
  document,
  "script",
  "//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js"
);
gdt("init", "1201077621");

import userApi from "../../api/user";

export default {
  data() {
    return {
      title: "Hello",
      info: {},
      options: {},
      directPayIdlist: ["54", "61"],
    };
  },
  onLoad(option) {
    uni.showLoading({
      title: "加载中",
    });
    this.options = option;
    uni.removeStorageSync("dy_source");
    uni.removeStorageSync("tx_source");
    uni.setStorageSync("click_url", location.href);
    if (option.clickid) {
      userApi
        .courseClick({ click_id: option.clickid, event_type: "view" })
        .then((res) => {});
      uni.setStorageSync("clickid", option.clickid);
      //记录抖音广告素材
      uni.setStorageSync("dy_source", option);
    }
    if (option.bd_vid) {
      uni.setStorageSync("bdVid", option.bd_vid);
    }
    if (option.click_id) {
      uni.setStorageSync("trackId", option.click_id.toString());
      //记录腾讯广告素材
      uni.setStorageSync("tx_source", option);
    }

    if (option.tw_callback) {
      uni.setStorageSync("tw_callback", option.tw_callback);
    }
    this.init(option);
  },
  methods: {
    init(option) {
      console.log("1111 :>> ", 1111);
      let teacher_id = this.options.teacher_id || "";
      let source = this.options.source || "";
      let tenxun_adv = this.options.click_id || "";
      let po_id = this.options.po_id || "";
      let recommend_id = this.options.recommend_id || "";
      let is_train = this.options.is_train || "";
      let id;
      gdt("track", "VIEW_CONTENT");
      if (option.merchandise_id) {
        id = option.merchandise_id;
        if (this.directPayIdlist.includes(id)) {
          uni.navigateTo({
            url: `/pages/directPayment/direct-payment?id=${id}&source=${source}&teacher_id=${teacher_id}&tenxun_adv=${tenxun_adv}&po_id=${po_id}&recommend_id=${recommend_id}&is_train=${is_train}`,
          });
          window.location.reload();
        }
      } else {
        id = 2;
      }
      userApi
        .GetMerchandiseInfo({ id: id, source: this.options.source })
        .then((res) => {
          uni.hideLoading();
          __bl.sum(res.data.source_name);
          this.info = res.data;
        });
    },
    goOrder() {
      let teacher_id = this.options.teacher_id || "";
      let source = this.options.source || "";
      let tenxun_adv = this.options.click_id || "";
      let po_id = this.options.po_id || "";
      let old_course = this.options.old_course || "";
      let merchandise_id = this.options.id || "";
      let recommend_id = this.options.recommend_id || "";
      let is_train = this.options.is_train || "";
      if (["172"].includes(merchandise_id)) {
        //添加自定义属性
        hina.track("classes_apply_click", {
          classes_type: "聂道少儿围棋",
        });
        console.log("hina :>> ", hina);
      }
      let timer = setTimeout(() => {
        uni.navigateTo({
          url: `/pages/order/order?id=${this.info.id}&source=${source}&mt_name=${this.info.mt_name}&teacher_id=${teacher_id}&tenxun_adv=${tenxun_adv}&po_id=${po_id}&old_course=${old_course}&recommend_id=${recommend_id}&is_train=${is_train}`,
        });
        clearTimeout(timer);
        window.location.reload();
      }, 1000);
    },
  },
};
</script>

<style>
.content {
  position: relative;
  padding-bottom: 88rpx;
}
.logo {
  width: 100vw;
}
.price {
  font-size: 56rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
.bottom_shop {
  width: 100vw;
  height: 196rpx;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  box-shadow: 0 2rpx 30rpx 0 rgba(45, 45, 45, 0.2);
  display: flex;
  align-items: center;
}
.shop_btn {
  width: 508rpx;
  height: 88rpx;
  border-radius: 44rpx;
  background: #ff6644;
  text-align: center;
  line-height: 88rpx;
  color: #fff;
  font-size: 34rpx;
  box-shadow: 0 6rpx 30rpx 0 rgba(255, 102, 68, 0.5);
  animation-name: scaleDraw; /*关键帧名称*/
  animation-timing-function: ease-in-out; /*动画的速度曲线*/
  animation-iteration-count: infinite; /*动画播放的次数*/
  animation-duration: 2s; /*动画所花费的时间*/
}
@keyframes scaleDraw {
  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
  0% {
    transform: scale(1); /*开始为原始大小*/
  }
  25% {
    transform: scale(0.9); /*放大1.1倍*/
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.9);
  }
}
</style>
