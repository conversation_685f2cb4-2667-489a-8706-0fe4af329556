<template>
  <view class="success">
    <view class="success_top">
      <image class="image_6" src="../../static/success.png" />
      <view class="success_box">
        <view class="success_title">报名成功</view>
        <view class="line"></view>
        <view class="col">
          <view style="margin-bottom: 24rpx">
            <text class="text_left">课程名称</text>
            <text class="text_right">{{ merchandise_name }}</text>
          </view>
          <view v-if="start_time">
            <text class="text_left">开课日期</text>
            <text class="text_right">{{ start_time }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="success_detail">
      <view class="hint">长按识别二维码，添加专属辅导老师微信</view>
      <view class="code">
        <image :show-menu-by-longpress="true" :src="qr_code" class="code_img" />
      </view>
      <view class="code_hint">长按识别二维码</view>
      <view class="line"></view>
      <view class="grid">
        <view class="flex-row space-x-13">
          <image class="image_8" src="../../static/radio.png" />
          <text class="font_3">优质学习资料</text>
        </view>
        <view class="flex-row space-x-13">
          <image class="image_9" src="../../static/radio.png" />
          <text class="font_3">课程上课提醒</text>
        </view>
        <view class="flex-row space-x-13">
          <image class="image_9" src="../../static/radio.png" />
          <text class="font_3">课程优惠资讯</text>
        </view>
        <view class="flex-row space-x-13">
          <image class="image_9" src="../../static/radio.png" />
          <text class="font_3">学习方法分享</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      qr_code: "",
      start_time: "",
      merchandise_name: "",
    };
  },
  onLoad(option) {
    this.start_time = uni.getStorageSync("start_time");
    this.qr_code = option.qr_code;
    this.merchandise_name = option.merchandise_name;
  },
  methods: {
    previewImage(e) {
      uni.previewImage({
        urls: [],
        current: "../../static/erweima.png",
        indicator: "default",
        loop: false,
        success: (res) => {
          console.log(res);
        },
      });
    },
  },
};
</script>

<style>
.success {
  width: 100vw;
  height: 100vh;
  background: #fff6f4;
  overflow: hidden;
}
.success_top {
  width: 670rpx;
  height: 336rpx;
  border-radius: 35rpx;
  background: #fff;
  margin: 74rpx 40rpx 32rpx 40rpx;
  position: relative;
  box-sizing: border-box;
  padding: 72rpx 50rpx 39rpx 50rpx;
}
.image_6 {
  width: 88rpx;
  height: 88rpx;
  position: absolute;
  top: -44rpx;
  left: 50%;
  transform: translateX(-50%);
}
.success_title {
  font-size: 40rpx;
  font-weight: bold;
  text-align: center;
}
.line {
  width: 570rpx;
  height: 1rpx;
  background: #cdcdce;
  margin: 32rpx 0;
}
.text_left {
  font-size: 32rpx;
  margin-right: 32rpx;
}
.text_right {
  font-size: 32rpx;
  color: #ff6644;
}
.success_detail {
  width: 670rpx;
  height: 838rpx;
  border-radius: 35rpx;
  background: #fff;
  margin: 0rpx 40rpx 32rpx 40rpx;
  box-sizing: border-box;
  padding: 48rpx 50rpx 58rpx 50rpx;
}
.hint {
  font-size: 32rpx;
  text-align: center;
}
.code {
  width: 400rpx;
  height: 400rpx;
  background: #cdcdce;
  margin: 28rpx 85rpx 16rpx;
}
.code_img {
  width: 100%;
  height: 100%;
}
.code_hint {
  font-size: 26rpx;
  color: #bebebe;
  text-align: center;
}
.grid {
  padding: 0rpx 27rpx 0;
  height: 141rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  row-gap: 20rpx;
  column-gap: 62rpx;
}
.space-x-13 > view:not(:first-child),
.space-x-13 > text:not(:first-child),
.space-x-13 > image:not(:first-child) {
  margin-left: 13rpx;
}
.flex-row {
  display: flex;
  align-items: center;
}
.image_8 {
  margin: 6rpx 0;
  flex-shrink: 0;
  width: 30rpx;
  height: 30rpx;
}
.font_3 {
  font-size: 30rpx;
  font-family: "PingFang SC";
  line-height: 42rpx;
  color: #333333;
}
.image_9 {
  margin: 6rpx 0;
  flex-shrink: 0;
  border-radius: 4rpx;
  width: 30rpx;
  height: 30rpx;
}
</style>
