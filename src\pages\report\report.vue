<template>
  <view class="flex-col page">
    <div id="htmlCanvas" ref="imgCanvas">
      <view class="section_2">
        <image class="image_5" src="../../static/report_title.png" />
        <view class="text-wrapper">
          <text class="text_3">棋士23b4</text>
        </view>
        <view class="text_40">恭喜你成为围棋思维之星</view>
        <view class="flex-row space-x-30 equal-division">
          <view class="flex-row justify-center space-x-22 equal-division-item">
            <image class="image_6" src="../../static/star.png" />
            <view class="flex-col">
              <view class="flex-row">
                <text class="font_1">{{star}}</text>
                <text class="font_2 text_5">颗</text>
              </view>
              <text class="font_3 text_6">星星数量</text>
            </view>
          </view>
          <view class="flex-row justify-center space-x-15 equal-division-item">
            <image class="image_7" src="../../static/time.png" />
            <view class="flex-col">
              <view class="flex-row">
                <text class="font_1">{{time}}</text>
                <text class="font_2 text_5">分钟</text>
              </view>
              <text class="font_3 text_6">测评用时</text>
            </view>
          </view>
        </view>
        <view class="bg_line"></view>
        <view class="flex-row justify-between group_8">
          <view class="flex-col items-center text-wrapper_2">
            <text class="font_4">能力雷达图</text>
          </view>
          <view class="flex-row space-x-7 section_4" @click="imageType">
            <image class="image_8" src="../../static/err.png" />
            <text class="text_7">能力解读</text>
          </view>
        </view>
        <view class="group_7">
          <image v-if="!type_on" class="image_9" :src="star_image" />
          <view class="flex-col space-y-10 section_7" v-else>
            <view class="font_5 text_9"><text class="text_weight">1. 观察力：</text>大脑对事物观察的能力。</view>
            <view class="font_5 text_9">主要影响：平面几何/基础判断/目标分析等。</view>
            <view class="font_5"><text class="text_weight">2. 计算力</text>：根据已知条件推算未知的能力。</view>
            <view class="font_5 text_9">主要影响：综合运算/心算/方程等。</view>
            <view class="font_5 text_10"><text class="text_weight">3. 想象力：</text>在大脑中描绘图像的能力。</view>
            <view class="font_5 text_9">主要影响：平面几何/空间几何/逻辑推理等。</view>
            <view class="font_5 text_11"><text class="text_weight">4. 应用能力：</text>根据自身掌握的知识和生活经 验对事物的处理能力。</view>
            <view class="font_5 text_9">主要影响：事实判断/逻辑推理/知识迁移和转化等。</view>
            <view class="font_5 text_12"><text class="text_weight">5. 专注力：</text>持续且坚持自控的能力。</view>
            <view class="font_5 text_9">主要影响：学习效率/深度等。</view>
          </view>
        </view>
        <view class="flex-col space-y-20 group_9">
          <view class="flex-col items-center text-wrapper_2">
            <text class="font_4">分享报告</text>
          </view>
          <text class="text_8">轻松拿捏围棋3路对局，快保存报告分享到朋友圈吧～</text>
        </view>
      </view>
    </div>

    <view class="flex-col section_3">
      <view class="flex-col text-wrapper_3" @click="toImage">
        <text class="text_11">生成海报</text>
      </view>
    </view>
    <uni-popup ref="popup" type="center">
      <view class="pop_image">
        <image class="image_10" :src="imgUrl" />
        <view class="btn_group">
          <view class="btn">长按图片保存</view>
        </view>
      </view>
      <view class="btn_close" @click="close"></view>
    </uni-popup>
  </view>
</template>
<script>
import html2canvas from "../../components/html2canvas";
export default {
  data() {
    return {
      type_on: false,
      star: "",
      question_right: 0,
      time: 0,
      star_image: "../../static/image_1.png",
      showPoster: true,
      imgUrl: "" // 用于存储base64图片
    };
  },
  onLoad(option) {
    this.time = Number(option.mm) + 1;
    var game_result = uni.getStorageSync("game_result");
    var question_result = JSON.parse(uni.getStorageSync("question_result"));
    question_result.forEach(t => {
      if (t == "Y") {
        this.question_right += 1;
      }
    });
    if (this.question_right >= 16) {
      this.star = 5;
      this.star_image = "../../static/image_3.png";
    } else if (this.question_right < 16 && this.question_right >= 10) {
      this.star = 4;
      this.star_image = "../../static/image_2.png";
    } else {
      this.star = 3;
      this.star_image = "../../static/image_1.png";
    }
  },
  methods: {
    imageType() {
      this.type_on = !this.type_on;
    },
    //生成图片
    toImage() {
      var that = this;
      uni.showLoading({
        title: "图片生成中"
      });
      html2canvas(this.$refs.imgCanvas, {
        backgroundColor: null,
        useCORS: true // 如果截图的内容里有图片,可能会有跨域的情况,加上这个参数,解决文件跨域问题
      })
        .then(canvas => {
          let url = canvas.toDataURL("image/png");
          that.imgUrl = url;
          uni.hideLoading();
          that.$refs.popup.open();
        })
        .catch(err => {
          uni.showToast({
            title: err,
            duration: 1000,
            icon: "none"
          });
        });
    },
    close(){
      this.$refs.popup.close();
    }
  }
};
</script>
<style scoped>
.page {
  background-color: #f7f9fc;
  background-image: linear-gradient(
    180deg,
    #5ad5fe 0%,
    #5ad5fe 9.61%,
    #8ae3fe 75.75%,
    #8ae3fe 100%
  );
  width: 100vw;
  overflow-y: auto;
  overflow-x: hidden;
  /* height: 100vh; */
  padding-bottom: 120rpx;
  padding-top: 48rpx;
}
#htmlCanvas {
  padding: 82rpx 0 120rpx;
}
.section_2 {
  margin: 0 32rpx;
  background-image: url("../../static/report_bg.png");
  background-position: 0% 0%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  padding: 1rpx;
}
.bg_line {
  width: 100%;
  height: 40rpx;
  background-image: url("../../static/bg_line.png");
  background-position: 0% 0%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 40rpx 0;
}

.section_3 {
  padding: 20rpx 0 88rpx;
  align-self: flex-start;
  background-color: #fdfeff;
  box-shadow: 0px -8rpx 40rpx 0px #2d2d2d0a;
  width: 750rpx;
  position: fixed;
  bottom: 0;
  left: 0;
}
.text-wrapper_3 {
  margin: 0 40rpx;
  padding: 20rpx 0;
  background-color: #31bfff;
  box-shadow: 0px 6rpx 20rpx 0px #31bfff40;
  border-radius: 44rpx;
  width: 670rpx;
  text-align: center;
  line-height: 48rpx;
}
.text-wrapper_4 {
  margin: 0 40rpx;
  padding: 20rpx 0;
  background-color: #cacdd2;
  border-radius: 44rpx;
  width: 670rpx;
  text-align: center;
  line-height: 48rpx;
}
.text_11 {
  color: #ffffff;
}
.image_5 {
  width: 338.5rpx;
  height: 164rpx;
  position: absolute;
  top: -82rpx;
  left: 50%;
  transform: translateX(-50%);
}
.text-wrapper {
  background-color: #e7f7ff;
  border-radius: 32rpx;
  width: 205rpx;
  border: solid 2rpx #31bfff;
  height: 60rpx;
  margin: 90rpx 233rpx 0 233rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text_3 {
  color: #31bfff;
  font-size: 32rpx;
}
.text_40 {
  color: #3f3f3f;
  font-size: 42rpx;
  /* font-family: ".AppleSystemUIFont"; */
  line-height: 50rpx;
  width: 473rpx;
  margin: 24rpx auto 40rpx;
  /* position: absolute;
  top: 159rpx; */
  /* margin: 24rpx 0 40rpx 120rpx; */
  /* transform: translateX(-50%); */
}
.space-x-30 > view:not(:first-child),
.space-x-30 > text:not(:first-child),
.space-x-30 > image:not(:first-child) {
  margin-left: 30rpx;
}
.equal-division {
  margin-top: 35rpx;
  display: flex;
}
.space-x-22 > view:not(:first-child),
.space-x-22 > text:not(:first-child),
.space-x-22 > image:not(:first-child) {
  margin-left: 22rpx;
}
.image_6 {
  align-self: center;
  width: 96rpx;
  height: 96rpx;
}
.space-x-5 > view:not(:first-child),
.space-x-5 > text:not(:first-child),
.space-x-5 > image:not(:first-child) {
  margin-left: 5rpx;
}
.group_6 {
  padding-left: 31rpx;
  padding-right: 7rpx;
}
.font_1 {
  font-size: 56rpx;
  font-family: "PingFang SC";
  line-height: 78rpx;
  font-weight: 500;
  color: #ff7e00;
}
.font_2 {
  font-size: 26rpx;
  font-family: "PingFang SC";
  line-height: 37rpx;
  color: #8c8c8c;
}
.text_5 {
  margin-top: 29rpx;
}
.font_3 {
  font-size: 26rpx;
  font-family: "PingFang SC";
  line-height: 37rpx;
  font-weight: 500;
  color: #515151;
}
.space-x-15 > view:not(:first-child),
.space-x-15 > text:not(:first-child),
.space-x-15 > image:not(:first-child) {
  margin-left: 15rpx;
}
.equal-division-item {
  flex: 1 1 290rpx;
  /* padding: 16rpx 32rpx 28rpx; */
  background-color: #edf9ff;
  border-radius: 20rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx;
}
.image_7 {
  margin-top: 19rpx;
  border-radius: 50%;
  width: 88rpx;
  height: 88rpx;
}
.text_6 {
  align-self: center;
}
.group_8 {
  /* margin-top: 120rpx; */
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
}
.text-wrapper_2 {
  padding: 3rpx 0;
  background-color: #31bfff;
  border-radius: 24rpx;
  width: 196rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.font_4 {
  font-size: 30rpx;
  font-family: "PingFang SC";
  line-height: 42rpx;
  font-weight: 600;
  color: #ffffff;
}
.space-x-7 > view:not(:first-child),
.space-x-7 > text:not(:first-child),
.space-x-7 > image:not(:first-child) {
  margin-left: 7rpx;
}
.section_4 {
  padding: 3rpx 20rpx 5rpx;
  background-color: #0000000f;
  border-radius: 24rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
}
.image_8 {
  margin: 7rpx 7rpx 5rpx;
  flex-shrink: 0;
  width: 29rpx;
  height: 28rpx;
}
.text_7 {
  color: #ff2c39;
  font-size: 28rpx;
  font-family: "PingFang SC";
  font-weight: 600;
  line-height: 40rpx;
}
.group_7 {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.image_9 {
  width: 610rpx;
  height: 518rpx;
}
.text_8 {
  color: #545454;
  font-size: 28rpx;
  font-family: "PingFang SC";
  font-weight: 500;
  line-height: 42rpx;
  text-align: left;
}
.group_9 {
  margin: 40rpx 30rpx;
}
.section_7 {
  padding: 31rpx 30rpx 30rpx ;
  margin: 0 30rpx;
  box-sizing: border-box;
  background-color: #edf9ff;
  border-radius: 20rpx;
  width: 610rpx;
  /* height: 518rpx; */
}
.space-y-10 > view:not(:first-child),
.space-y-10 > text:not(:first-child),
.space-y-10 > image:not(:first-child) {
  margin-top: 10rpx;
}
.font_5 {
  font-size: 28rpx;
  font-family: "PingFang SC";
  line-height: 40rpx;
  font-weight: 500;
  color: #545454;
}
.text_9 {
  align-self: flex-start;
}
.text_10 {
  align-self: flex-start;
}
.text_11 {
  line-height: 42rpx;
  text-align: left;
}
.text_12 {
  align-self: flex-start;
}
.pop_image {
/*  */
  background:#8ae3fe;
  /* width: 70%;
  height: 70%; */
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.image_10 {
  width: 590rpx;
  height: 1260rpx;
}
.btn_group {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 60rpx;
  width: 100%;
  height: 148rpx;
  background: #ffffff;
  position: absolute;
  bottom: 0;
  box-shadow: 0 -2rpx 13rpx 0 #E5E5E5;
  border-radius: 0 0 40rpx 40rpx;
}
.btn_close {
  width: 80rpx;
  height: 80rpx;
  background: url("../../static/close.png") no-repeat;
  background-size: 100%;
  margin-top: 40rpx;
  margin-left: 251rpx;
}
.btn {
  width: 408rpx;
  height: 88rpx;
  margin-left: 16rpx;
  line-height: 88rpx;
  background: #31BFFF;
  border-radius: 44rpx;
  font-size: 34rpx;
  color: #ffffff;
  text-align: center;
}
.text_weight{
  font-weight: bolder;
}
</style>