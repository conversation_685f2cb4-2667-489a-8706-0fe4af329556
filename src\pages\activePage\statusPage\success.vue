<template>
  <div class="success-page">
    <div class="status-box">
      <img src="../../../../public/active/成功.png" alt="">
      <div class="success-text">{{ successText }}</div>
    </div>
    <div class="qrcode">
      <div class="qrcode-bg">
        <img :src="qrcodeSrc" alt="">
      </div>
      <div class="tips">长按扫描二维码，添加班主任老师</div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      successText: "",
      qrcodeSrc: ""
    }
  },
  computed: {},
  methods: {},
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    this.successText = option.text;
    this.qrcodeSrc = option.qrcodeSrc
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
} 
</script>

<style lang="scss" scoped>
.success-page {
  width: 100%;
  height: 90vh;
  padding-top: 200rpx;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  .status-box {
    text-align: center;
    padding-bottom: 120rpx;
    img {
      width: 240rpx;
    }
    .success-text {
      margin-top: 20rpx;
      font-size: 45rpx;
      color: #fc606e;
      font-weight: 600;
    }
  }
  .qrcode {
    text-align: center;
    .qrcode-bg {
      background: #f7cec4;
      border-radius: 10rpx;
      width: 380rpx;
      height: 380rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      img {
        width: calc(100% - 30rpx);
        height: calc(100% - 30rpx);
        padding: 30rpx;
      }
    }
    .tips {
      margin-top: 20rpx;
      font-size: 28rpx;
    }
  }
}
</style>