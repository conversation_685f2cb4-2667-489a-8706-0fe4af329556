user  root;
worker_processes  1;

pid        /var/run/nginx.pid;


events {
    worker_connections  10240;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;
    client_max_body_size 128M;
    gzip  on;
    gzip_types text/plain application/x-javascript application/javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;

    server {
            listen       80;
            location ~* \.(txt)$ {
			root /etc/nginx/mp;
		}
            location / {
                root   /etc/nginx/html;
                index  index.html index.htm;
                try_files $uri $uri/ /index.html;
            }
        }
}
