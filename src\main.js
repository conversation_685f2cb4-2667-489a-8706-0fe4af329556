import Vue from "vue";
import App from "./App";
// import store from "./store";
import axios from "./http.js";
import moment from "moment";
Vue.prototype.moment = moment;
Vue.config.productionTip = false;
const BrowserLogger = require('alife-logger');
const __bl = BrowserLogger.singleton(
  {
    pid:"j711v9sbe7@f2eecc34bd46ee1",
    appType:"web",
    imgUrl:"https://arms-retcode.aliyuncs.com/r.png?",
    sendResource:true,
    enableLinkTrace:true,
    behavior:true,
    enableSPA:true,
    useFmp:true,
    enableConsole:true
  }
);

import hina from 'hina-cloud-js-sdk'
hina.init({
  serverUrl: 'https://higateway.haishuu.com/gather?project=YTJ_ancda_1&token=39Tt5S4M',
  autoTrackConfig: {
    pageviewAutoTrack: 'singlePage',
    pageLeaveAutoTrack: true,
  },
});
// 将 SDK 实例赋给全局变量 hina
window["hina"] = hina;
App.mpType = "app";
Vue.prototype.$axios = axios;
// Vue.prototype.$store = store;
const app = new Vue({
  ...App,
  __bl
});
app.$mount();
