<template>
  <div v-if="isDesktop" class="isDesktop">请在手机端打开</div>
</template>

<script>
import userApi from "@/api/user";
export default {
  data() {
    return {
      isMobile: false,
      isDesktop: false,
    };
  },
  onLoad(options) {
    if (
      navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|IEMobile)/i
      )
    ) {
      this.isMobile = true;
    } else {
      this.isDesktop = true;
    }
    if (!this.isMobile) {
      return;
    }
    userApi
      .GetPayResult({
        order_number: this.options.order_id,
      })
      .then((res) => {
        console.log("res :>> ", res);
        const { qr_code } = res.data;
        this.options.qr_code = qr_code ?? "";
        userApi
          .Generate_urllink({
            query: this.jsonToUrlParams(options),
            path: "pages/signUp/success",
          })
          .then((res) => {
            const url = res.data;
            if (url) {
              window.location.href = url;
            }
          })
          .catch(() => {
            uni.showToast({
              title: "获取打开小程序链接失败，请重试！",
              duration: 3000,
              icon: "none",
            });
          });
      })
      .catch(() => {
        uni.showToast({
          title: "获取打开小程序链接失败，请重试！",
          duration: 3000,
          icon: "none",
        });
      });
  },
  methods: {
    jsonToUrlParams(json) {
      return Object.entries(json)
        .map(([key, value]) => `${key}=${value}`)
        .join("&");
    },
  },
};
</script>

<style lang="scss" scoped>
.isDesktop {
  display: grid;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 14px;
  font-weight: 600;
}
</style>
