import axios from "../http";

function getMerchandiseInfo(data) {
  return axios
    .get("/api/public/course/merchandise", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getCourseInfo(data) {
  return axios
    .get("/api/public/course/course", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getPayResult(data) {
  return axios
    .get("/api/public/order/result", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getSendSMS(data) {
  return axios
    .post("/api/public/user/sendSMS", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function createOrder(data) {
  return axios
    .post("/api/public/order/create", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      uni.showToast({
        title: error,
      });
      console.log(error);
      /* eslint-enable */
    });
}

function weChatH5Auth(data) {
  return axios
    .post("/api/wechat-service/public/h5-auth", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}

function weChatH5Sign(data) {
  return axios
    .post("/api/wechat-service/public/h5-sign", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
// 抖音回传
function courseClick(data) {
  return axios
    .post("/api/public/course/click", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
// 百度回传
function toBaidu(data) {
  return axios
    .post("/api/public/baidu/service", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}

// 小红书回传
function toRed(data) {
  return axios
    .post("/api/public/xhs/service", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
// 生成打开小程序链接
function generate_urllink(data) {
  return axios
    .post("/api/public/applet/url-link", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return Promise.reject(error);
    });
}
function getClassroomByClassTime(data) {
  return axios
    .get("/api/public/course/classrooms", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
export default {
  async Generate_urllink(data) {
    return generate_urllink(data);
  },
  async GetMerchandiseInfo(data) {
    return getMerchandiseInfo(data);
  },
  async GetCourseInfo(data) {
    return getCourseInfo(data);
  },
  async GetSendSMS(data) {
    return getSendSMS(data);
  },
  async CreateOrder(data) {
    return createOrder(data);
  },
  async GetPayResult(data) {
    return getPayResult(data);
  },
  async weChatH5Auth(data) {
    return weChatH5Auth(data);
  },
  async weChatH5Sign(data) {
    return weChatH5Sign(data);
  },
  async courseClick(data) {
    return courseClick(data);
  },
  async toBaidu(data) {
    return toBaidu(data);
  },
  async toRed(data) {
    return toRed(data);
  },
  async getClassroomByClassTime(data) {
    return getClassroomByClassTime(data);
  },
};
