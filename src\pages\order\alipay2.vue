<template>
  <div class="alipay2">
    <div class="success-page" v-if="isPaySuccess">
      <div class="status-box">
        <img src="../../../public/active/成功.png" alt="">
        <div class="success-text">{{ successText }}</div>
      </div>
      <div class="qrcode">
        <div class="qrcode-bg">
          <img :src="qrcodeSrc" alt="">
        </div>
        <div class="tips">长按扫描二维码，添加班主任老师</div>
      </div>
    </div>
  </div>
</template>

<script>
import activeApi from "../../api/active1";
export default {
  components: {},
  data() {
    return {
      successText: "支付成功",
      isPaySuccess: false,
      pay_url: "",
      qrcodeSrc: "",
      order_number: "",
      payListenerTimer: null,
    }
  },
  computed: {},
  methods: {
    alipay_ready(callback) {
      // 如果jsbridge已经注入则直接调用
      if (window.AlipayJSBridge) {
        callback && callback();
      } else {
        // 如果没有注入则监听注入的事件
        document.addEventListener("AlipayJSBridgeReady", callback, false);
      }
    },
    payListener(order_number) {

      if (this.payListenerTimer) {
        clearInterval(this.payListenerTimer);
      }
      this.payListenerTimer = setInterval(() => {
        activeApi.GetPayResult({ order_number }).then((res) => {
          // alert(JSON.stringify(res));
          console.log(res, "GetPayResult")
          if (res.data.pay_status != "in_pay") {
            clearInterval(this.payListenerTimer);
          } else {
            uni.hideLoading();
          }
          if (res.data.pay_status == "no_pay") {
            uni.showToast({
              title: "支付失败",
              duration: 2000,
              icon: "none",
            });
            // uni.navigateTo({
            //   url: `/pages/activePage/statusPage/error?text=支付失败&qrcodeSrc=${res.data.qr_code}`,
            // });
          }
          if (res.data.pay_status == "is_pay") {
            // alert(JSON.stringify(res));
            uni.showToast({
              title: "支付成功",
              duration: 2000,
              icon: "success",
            });
            this.qrcodeSrc = res.data.qr_code;
            clearInterval(this.payListenerTimer);
            this.isPaySuccess = true;
            // window.history.go(-1);
            // uni.navigateTo({
            //   url: `/pages/activePage/statusPage/success?text=支付成功&qrcodeSrc=${res.data.qr_code}`,
            // });
          }
        });
      }, 1000);
    },
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    const { pay_url } = option;
    const str_arr = pay_url.split("_order_number_");
    this.pay_url = str_arr[0];
    this.order_number = pay_url.split("_order_number_")[1];
    this.alipay_ready(() => {
      if (this.pay_url) {
        AlipayJSBridge.call("pushWindow", {
          url: this.pay_url,
          param: {
            readTitle: true,
            showOptionMenu: false,
          },
        });
      }

      // alert(pay_url);
      // AlipayJSBridge.call(
      //   "tradePay",
      //   {
      //     tradeNO: pay_url,
      //   },
      //   function (result) {
      //     alert(JSON.stringify(result));
      //   }
      // );
    });
    // alert(this.order_number);
    if (this.order_number) {
      this.payListener(this.order_number);
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
} 
</script>

<style lang="scss" scoped>

.success-page {
  width: 100%;
  height: 90vh;
  padding-top: 200rpx;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  .status-box {
    text-align: center;
    padding-bottom: 120rpx;
    img {
      width: 240rpx;
    }
    .success-text {
      margin-top: 20rpx;
      font-size: 45rpx;
      color: #fc606e;
      font-weight: 600;
    }
  }
  .qrcode {
    text-align: center;
    .qrcode-bg {
      background: #f7cec4;
      border-radius: 10rpx;
      width: 380rpx;
      height: 380rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      img {
        width: calc(100% - 30rpx);
        height: calc(100% - 30rpx);
        padding: 30rpx;
      }
    }
    .tips {
      margin-top: 20rpx;
      font-size: 28rpx;
    }
  }
}
</style>