<template>
  <view class="flex-col page">
    <view class="flex-col space-y-582 section_2">
      <view class="flex-col space-y-40 group_4">
        <view class="flex-row justify-between">
          <view class="group_5">
            <text class="text_3">{{index+1}}</text>
            <text class="text_4">/{{question_list.length}}</text>
          </view>
          <view class="flex-col text-wrapper">
            <text class="text_5">用时：{{mm<10?"0"+mm:mm}}:{{ss<10?"0"+ss:ss}}</text>
          </view>
        </view>
        <view v-if="question_list[index].type=='image'">
          <image class="image_6" :src="question_list[index].image" />
          <view class="flex-row group_7">
            <text class="font_1 text_6">
              {{question_list[index].name}}
              <image
                class="image_5"
                src="../../static/trumpet.png"
                @click="playVoice(question_list[index].name)"
              />
            </text>
          </view>
          <view class="grid">
            <view :key="i" v-for="(item, i) in question_list[index].choices" @click="answerClick(item)">
              <view
                class="flex-row justify-center"
                :class="[
            question_list[index].answer_result&&Object.keys(item)[0] == question_list[index].answer
              ? 'grid-item_success'
              : question_list[index].answer_result==='E'&&Object.keys(item)[0] == question_list[index].user_answer
              ? 'grid-item_err'
              : 'grid-item'
          ]"
              >
                <image
                  class="text-wrapper_2 position_1"
                  src="../../static/anser_success.png"
                  v-if="question_list[index].answer_result&&Object.keys(item)[0] == question_list[index].answer"
                />
                <image
                  class="text-wrapper_2 position_1"
                  src="../../static/anser_err.png"
                  v-else-if="question_list[index].answer_result==='E'&&Object.keys(item)[0] == question_list[index].user_answer"
                />
                <view class="text-wrapper_2 position_1" v-else>
                  <text class="font_3">{{Object.keys(item)[0]}}</text>
                </view>
                <text>{{Object.values(item)[0]}}</text>
              </view>
            </view>
          </view>
        </view>
        <view v-else>
          <view class="flex-row space-x-12 group_title">
            <text class="font_1 text_6">
              {{question_list[index].name}}
              <image
                class="image_5"
                src="../../static/trumpet.png"
                @click="playVoice(question_list[index].name)"
              />
            </text>
          </view>
          <view class="flex-col space-y-24 group_6">
            <view>
              <view
                class="flex-row space-x-32 list-item"
                :key="i"
                v-for="(item, i) in question_list[index].choices"
                @click="answerClick(item)"
                :class="[
            question_list[index].answer_result&&Object.keys(item)[0] == question_list[index].answer
              ? 'list-item_success'
              : question_list[index].answer_result==='E'&&Object.keys(item)[0] == question_list[index].user_answer
              ? 'list-item_err'
              : 'list-item']"
              >
                <image
                  class="text-wrapper_2 position_1"
                  src="../../static/anser_success.png"
                  v-if="question_list[index].answer_result&&Object.keys(item)[0] == question_list[index].answer"
                />
                <image
                  class="text-wrapper_2 position_1"
                  src="../../static/anser_err.png"
                  v-else-if="question_list[index].answer_result==='E'&&Object.keys(item)[0] == question_list[index].user_answer"
                />
                <view class="flex-col items-center text-wrapper_2 position_1" v-else>
                  <text class="font_3">{{Object.keys(item)[0]}}</text>
                </view>
                <text class="text_7">{{Object.values(item)[0]}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="flex-col section_3" v-if="!question_list[index].answer_result">
      <view class="flex-col items-center text-wrapper_4">
        <text class="font_1 text_11">下一题</text>
      </view>
    </view>
    <view class="flex-col section_3" v-else-if="(this.index+1) == question_list.length">
      <view class="flex-col items-center text-wrapper_3" @click="goGame">
        <text class="font_1 text_11">进入对局</text>
      </view>
    </view>
    <view class="flex-col section_3" v-else>
      <view class="flex-col items-center text-wrapper_3" @click="next">
        <text class="font_1 text_11">下一题</text>
      </view>
    </view>
  </view>
</template>
<script>
const data_list = require("../../components/evaluation");
export default {
  data() {
    return {
      type: "image",
      question_list: "",
      index: 0,
      answer_result: "",
      user_answer: "",
      ss: 0,
      mm: 0,
      timer: "",
      match_id: ""
    };
  },
  onLoad(option) {
    this.question_list = JSON.parse(JSON.stringify(data_list.list));
    this.timeChange();
    this.match_id = option.match_id;
  },
  beforeDestroy() {
    console.log(11);
    clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    timeChange() {
      this.timer = setInterval(() => {
        if (this.ss == 59) {
          this.mm += 1;
          this.ss = 0;
        } else {
          this.ss += 1;
        }
      }, 1000);
    },
    answerClick(item) {
      if (!this.question_list[this.index].answer_result) {
        if (Object.keys(item)[0] === this.question_list[this.index].answer) {
          this.$nextTick(() => {
            this.question_list[this.index].answer_result = "Y";
            this.question_list = JSON.parse(JSON.stringify(this.question_list));
          });
        } else {
          this.$nextTick(() => {
            this.question_list[this.index].answer_result = "E";
            this.question_list[this.index].user_answer = Object.keys(item)[0];
            this.question_list = JSON.parse(JSON.stringify(this.question_list));
          });
        }
      }
    },
    next() {
      console.log(this.index);
      this.index += 1;
    },
    playVoice(answer_name) {
      var audio = uni.createInnerAudioContext();
      audio.src = `/static/audio/${answer_name}.mp3`;
      audio.onEnded(() => {
        audio.destroy();
      });
      audio.play();
    },
    goGame() {
      var question_result = [];
      this.question_list.forEach(t => {
        question_result.push(t.answer_result);
      });
      uni.setStorageSync("question_result", JSON.stringify(question_result));
      clearInterval(this.timer);
      this.timer = null;
      uni.navigateTo({
        url: `/pages/game/game?match_id=${this.match_id}&mm=${this.mm}&ss=${this.ss}`
      });
    }
  }
};
</script>
<style scoped>
.page {
  background-color: #f7f9fc;
  background-image: linear-gradient(
    180deg,
    #5ad5fe 0%,
    #5ad5fe 9.61%,
    #8ae3fe 75.75%,
    #8ae3fe 100%
  );
  width: 100vw;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
}
.section_2 {
  height: 100%;
  margin: 32rpx 16rpx 0;
  padding-top: 24rpx;
  background-color: #ffffff;
  box-shadow: 0px 0px 25rpx 0px #2d2d2d1a;
  border-radius: 35rpx 35rpx 0px 0px;
}
.space-y-582 > view:not(:first-child),
.space-y-582 > text:not(:first-child),
.space-y-582 > image:not(:first-child) {
  margin-top: 582rpx;
}
.group_4 {
  margin: 0 19rpx;
  height: 100%;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.space-y-40 > view:not(:first-child),
.space-y-40 > text:not(:first-child),
.space-y-40 > image:not(:first-child) {
  margin-top: 40rpx;
}
.group_5 {
  height: 56rpx;
}
.text_3 {
  color: #16adfe;
  font-size: 40rpx;
  font-family: "PingFang SC";
  font-weight: 600;
  line-height: 56rpx;
}
.text_4 {
  color: #333333;
  font-size: 35rpx;
  font-family: "PingFang SC";
  font-weight: 600;
  line-height: 49rpx;
}
.text-wrapper {
  /* padding: 7rpx 0; */
  background-color: #e7f7ff;
  border-radius: 28rpx;
  width: 248rpx;
  height: 56rpx;
  text-align: center;
  line-height: 56rpx;
}
.text_5 {
  margin-left: 25rpx;
  margin-right: 22rpx;
  color: #2cc4fe;
  font-size: 30rpx;
  font-family: "PingFang SC";
  font-weight: 500;
  line-height: 42rpx;
}
.group_title {
  display: flex;
  margin-bottom: 20rpx;
  padding: 0 16rpx;
}
.group_6 {
  padding: 0 16rpx;
}
.space-x-12 > view:not(:first-child),
.space-x-12 > text:not(:first-child),
.space-x-12 > image:not(:first-child) {
  margin-left: 12rpx;
}
.font_1 {
  font-size: 34rpx;
  font-family: "PingFang SC";
  line-height: 48rpx;
  font-weight: 500;
}
.text_6 {
  margin: 3rpx 0;
  color: #333333;
  font-size: 48rpx;
  line-height: 54rpx;
}
.image_5 {
  border-radius: 50%;
  width: 54rpx;
  height: 54rpx;
  vertical-align: middle;
}
.space-y-24 > view:not(:first-child),
.space-y-24 > text:not(:first-child),
.space-y-24 > image:not(:first-child) {
  margin-top: 24rpx;
}
.list-item {
  /* padding: 8rpx 8rpx; */
  height: 88rpx;
  background-color: #f7f8f9;
  border-radius: 44rpx;
  display: flex;
  font-size: 32rpx;
  line-height: 45rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}
.list-item_success {
  /* padding: 8rpx 8rpx; */
  background-color: #09d67d;
  border-radius: 44rpx;
  display: flex;
  font-size: 32rpx;
  line-height: 45rpx;
  font-weight: 600;
  color: #ffffff;
}
.list-item_err {
  /* padding: 8rpx 8rpx; */
  background-color: #ff5f69;
  border-radius: 44rpx;
  display: flex;
  font-size: 32rpx;
  line-height: 45rpx;
  font-weight: 600;
  color: #ffffff;
}
.text-wrapper_2 {
  /* padding: 17rpx 0; */
  background-color: #ffffff;
  border-radius: 50%;
  width: 72rpx;
  height: 72rpx;
  text-align: center;
  line-height: 72rpx;
}
.font_3 {
  font-size: 32rpx;
  font-family: ".AppleSystemUIFont";
  line-height: 38rpx;
  color: #3b3b3b;
}
.text_7 {
  align-self: center;
}
.space-x-32 > view:not(:first-child),
.space-x-32 > text:not(:first-child),
.space-x-32 > image:not(:first-child) {
  margin-left: 32rpx;
}
.section_3 {
  padding: 20rpx 0 88rpx;
  align-self: flex-start;
  background-color: #fdfeff;
  box-shadow: 0px -8rpx 40rpx 0px #2d2d2d0a;
  width: 750rpx;
  position: fixed;
  bottom: 0;
  left: 0;
}
.text-wrapper_3 {
  margin: 0 40rpx;
  padding: 20rpx 0;
  background-color: #31bfff;
  box-shadow: 0px 6rpx 20rpx 0px #31bfff40;
  border-radius: 44rpx;
  width: 670rpx;
  text-align: center;
  line-height: 48rpx;
}
.text-wrapper_4 {
  margin: 0 40rpx;
  padding: 20rpx 0;
  background-color: #cacdd2;
  border-radius: 44rpx;
  width: 670rpx;
  text-align: center;
  line-height: 48rpx;
}
.text_11 {
  color: #ffffff;
}
.image_6 {
  width: 686rpx;
  height: 686rpx;
}
.group_7 {
  margin-top: 40rpx;
  padding: 0 8rpx;
  display: flex;
}
.grid {
  margin: 24rpx 8rpx 0;
  height: 192rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  row-gap: 18rpx;
  column-gap: 24rpx;
}
.grid-item {
  background-color: #f7f8f9;
  border-radius: 44rpx;
  position: relative;
  display: flex;
  width: 324rpx;
  height: 88rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.grid-item_err {
  background-color: #ff5f69;
  border-radius: 44rpx;
  position: relative;
  display: flex;
  width: 324rpx;
  height: 88rpx;
  position: relative;
  justify-content: center;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.grid-item_success {
  background-color: #09d67d;
  border-radius: 44rpx;
  position: relative;
  display: flex;
  width: 324rpx;
  height: 88rpx;
  position: relative;
  justify-content: center;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.position_1 {
  position: absolute;
  left: 8rpx;
  top: 8rpx;
}
</style>