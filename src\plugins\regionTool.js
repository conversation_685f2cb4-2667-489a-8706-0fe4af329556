
import provinceAndCityData from "./area";
function setChnRegion(provinceId, cityId) {
  try {
    let regionChn = {
      provinceChn: "",
      cityChn: ""
    };
    let regionOptions = provinceAndCityData;
    let getObjectByValue = function(array, key, value) {
      return array.filter(function(object) {
        return object[key] === value;
      });
    };
    let provinceObj = getObjectByValue(
      regionOptions,
      "value",
      provinceId.toString()
    );
    regionChn.provinceChn = provinceObj[0].label;
    let cityObj = getObjectByValue(
      provinceObj[0].children,
      "value",
      cityId.toString()
    );
    regionChn.cityChn = cityObj[0].label;
    return regionChn.provinceChn + "" + regionChn.cityChn;
  } catch (e) {
    return provinceId, cityId;
  }
}

export default {
  SetChnRegion(provinceId, cityId) {
    return setChnRegion(provinceId, cityId);
  }
};
