<template>
    <view class="flex-col space-y-128 page">
  <view class="flex-col space-y-60">
    <view class="flex-col space-y-40 group_4">
      <view class="flex-col">
        <image
          class="image_5"
          src="../../static/star_bg.png"
        />
        <view class="flex-col items-center text-wrapper view"><text class="font_1">评测规则</text></view>
        <view class="flex-col items-start space-y-12 group_5">
          <view class="font_2">测评时间：无限制，可以随时进入。</view>
          <view class="font_2">测评内容：习题闯关+三路棋盘对弈闯关</view>
        </view>
      </view>
      <view class="flex-col">
        <view class="flex-col items-center text-wrapper"><text class="font_1">温馨提示</text></view>
        <view class="flex-col space-y-18 group_5">
          <text class="font_2 text_3">
            1. 测评内容为体验课阶段知识点和围棋文化常识，请小棋手们认真作答，相信你们都是本期最佳小棋手哦！
          </text>
          <view class="group_6">
            <text class="font_2">2. 请仔细读题再作答哦，每题只有</text>
            <text class="font_2 text_4">一次</text>
            <text class="font_2">作答机会，请小棋手们先思考，再点击哦，加油吧！</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="flex-col section_2">
    <view class="flex-col items-center text-wrapper_2" @click="startAnswer"><text class="text_5">开始测评</text></view>
  </view>
</view>
</template>
<script>
import matchApi from "../../api/elaluation";
export default {
  data(){
    return {

    }
  },
  methods:{
    startAnswer(){
      matchApi.MatchStart().then(res=>{
        uni.navigateTo({
          url: `/pages/answer/answer?match_id=${res.data.match_id}`
        });
      })
    }
  }
}
</script>
<style scoped>
.space-y-128 > view:not(:first-child),
.space-y-128 > text:not(:first-child),
.space-y-128 > image:not(:first-child) {
  margin-top: 128rpx;
}
.page {
  background-color: #ffffff;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}
.space-y-60 > view:not(:first-child),
.space-y-60 > text:not(:first-child),
.space-y-60 > image:not(:first-child) {
  margin-top: 60rpx;
}
.space-y-43 > view:not(:first-child),
.space-y-43 > text:not(:first-child),
.space-y-43 > image:not(:first-child) {
  margin-top: 43rpx;
}
.section {
  padding: 28rpx 24rpx 21rpx;
  background-color: #f5f5f5;
}
.group {
  padding-left: 35rpx;
  padding-right: 5rpx;
}
.text {
  color: #000000;
  font-size: 28rpx;
  font-family: 'Helvetica';
  font-weight: 700;
  line-height: 34rpx;
}
.space-x-10 > view:not(:first-child),
.space-x-10 > text:not(:first-child),
.space-x-10 > image:not(:first-child) {
  margin-left: 10rpx;
}
.group_2 {
  margin: 6rpx 0 4rpx;
}
.image {
  width: 34rpx;
  height: 22rpx;
}
.image_2 {
  width: 31rpx;
  height: 22rpx;
}
.image_3 {
  width: 49rpx;
  height: 22.5rpx;
}
.group_3 {
  position: relative;
}
.image_4 {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 44rpx;
  height: 44rpx;
}
.text_2 {
  color: #000000;
  font-size: 36rpx;
  font-family: 'PingFang SC';
  font-weight: 600;
  line-height: 50rpx;
}
.space-y-40 > view:not(:first-child),
.space-y-40 > text:not(:first-child),
.space-y-40 > image:not(:first-child) {
  margin-top: 40rpx;
}
.group_4 {
  padding: 0 60rpx;
}
.image_5 {
  margin: 60rpx 20rpx 0;
  width: 590rpx;
  height: 520rpx;
}
.text-wrapper {
  background-color: #ff7748;
  border-radius: 24rpx;
  width: 166rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.view {
  margin-top: 40rpx;
}
.font_1 {
  font-size: 30rpx;
  font-family: 'PingFang SC';
  line-height: 42rpx;
  font-weight: 600;
  color: #ffffff;
}
.space-y-12 > view:not(:first-child),
.space-y-12 > text:not(:first-child),
.space-y-12 > image:not(:first-child) {
  margin-top: 12rpx;
}
.font_2 {
  font-size: 28rpx;
  font-family: 'PingFang SC';
  line-height: 42rpx;
  font-weight: 500;
  color: #333333;
}
.space-y-18 > view:not(:first-child),
.space-y-18 > text:not(:first-child),
.space-y-18 > image:not(:first-child) {
  margin-top: 18rpx;
}
.group_5 {
  padding-top: 24rpx;
}
.text_3 {
  text-align: left;
}
.group_6 {
  text-align: left;
}
.text_4 {
  color: #ff4100;
  font-weight: 600;
}
.section_2 {
  width: 100%;
  padding: 20rpx 0 88rpx;
  background-color: #fdfeff;
  box-shadow: 0px -8rpx 40rpx 0px #2d2d2d0a;
  position: fixed;
  bottom: 0;
  left: 0;
}
.text-wrapper_2 {
  margin: 0 40rpx;
  background-color: #31bfff;
  box-shadow: 0px 6rpx 20rpx 0px #31bfff40;
  border-radius: 44rpx;
  text-align: center;
  line-height: 88rpx;
}
.text_5 {
  color: #ffffff;
  font-size: 34rpx;
  font-family: 'PingFang SC';
  font-weight: 500;
  line-height: 48rpx;
}
</style>