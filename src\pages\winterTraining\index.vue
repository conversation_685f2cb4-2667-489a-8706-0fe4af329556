<template>
  <view class="winter-training">
    <scroll-view
      scroll-y
      class="scroll-container"
      :scroll-top="scrollTop"
      @scroll="handleScroll"
    >
      <view class="banner">
        <image class="banner-img" src="../../static/winter_banner.jpg" />
      </view>

      <view class="form-container">
        <view class="form-item">
          <text class="required">*</text>
          <text class="label">姓名</text>
          <input
            class="input"
            maxlength="20"
            v-model="formData.name"
            placeholder="请输入姓名"
            placeholder-style="color: #999"
          />
        </view>

        <view class="form-item">
          <text class="required">*</text>
          <text class="label">系统课手机号</text>
          <input
            class="input"
            type="number"
            maxlength="11"
            v-model.trim="formData.mobile"
            placeholder="请输入手机号"
            placeholder-style="color: #999"
          />
        </view>

        <view class="form-item">
          <text class="required">*</text>
          <text class="label">选择日期</text>
          <picker
            class="picker-item"
            :range="periodList"
            range-key="dateRange"
            :value="selectedPeriodIndex"
            @change="handleDateChange"
          >
            <view class="picker">
              {{
                selectedPeriodIndex >= 0
                  ? periodList[selectedPeriodIndex].dateRange
                  : "请选择日期"
              }}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="required">*</text>
          <text class="label">选择时间</text>
          <picker
            class="picker-item"
            :range="timeList"
            range-key="time"
            :value="selectedTimeIndex"
            :disabled="selectedPeriodIndex < 0"
            @change="handleTimeChange"
          >
            <view class="picker">
              {{
                selectedTimeIndex >= 0
                  ? timeList[selectedTimeIndex].time
                  : "请选择时间"
              }}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="required">*</text>
          <text class="label">授课讲师</text>
          <view class="input teacher-input">
            {{ formData.teacherName || "" }}
          </view>
        </view>
      </view>

      <view class="footer-placeholder"></view>
    </scroll-view>

    <view class="footer">
      <text class="capacity">
        可选人数剩余<text class="num">{{ num }}</text
        >人
      </text>
      <button
        class="submit-btn"
        :class="{
          disabled: !isFormValid || btnDisabled || !open || num <= 0,
        }"
        :disabled="!isFormValid || btnDisabled || !open || num <= 0"
        @tap="handleSubmit"
      >
        {{ open ? "提交信息" : "报名未开始" }}
      </button>
    </view>
  </view>
</template>

<script>
import elaluationApi from "@/api/elaluation";
export default {
  data() {
    return {
      formData: {
        name: "",
        mobile: "",
        date: "",
        time: "",
        teacherName: "",
      },
      periodList: [],
      selectedPeriodIndex: -1,
      selectedTimeIndex: -1,
      num: "",
      btnDisabled: false,
      open: false,
      scrollTop: 0,
      keyboardHeight: 0,
    };
  },

  computed: {
    isFormValid() {
      const { name, mobile } = this.formData;
      return (
        name &&
        mobile &&
        mobile.length === 11 &&
        this.selectedPeriodIndex >= 0 &&
        this.selectedTimeIndex >= 0
      );
    },
    timeList() {
      if (this.selectedPeriodIndex >= 0) {
        return this.periodList[this.selectedPeriodIndex].timeList;
      }
      return [];
    },
  },
  created() {
    this.getTrainList();
    this.getTrainConfig();
  },
  methods: {
    //获取集训报名开关
    getTrainConfig() {
      elaluationApi.TrainConfig().then((res) => {
        console.log(res);
        this.open = res.data === "open";
      });
    },

    getTrainList() {
      elaluationApi.TrainList().then((res) => {
        console.log(res);
        this.periodList = res.data ?? [];
      });
    },
    //获取报名人数
    getSignedNumber() {
      const train_id = this.periodList[this.selectedPeriodIndex].timeList[
        this.selectedTimeIndex
      ].id;
      elaluationApi
        .SignedNumber({
          train_id,
        })
        .then((res) => {
          this.num = res.data;
        });
    },
    handleDateChange(e) {
      this.selectedPeriodIndex = Number(e.detail.value);
      this.selectedTimeIndex = -1;
      this.formData.date = this.periodList[this.selectedPeriodIndex].dateRange;
      if (this.formData.time > 0) {
        this.formData.teacherName = this.timeList[0].teacher;
      } else {
        this.formData.teacherName = "";
      }
    },

    handleTimeChange(e) {
      this.selectedTimeIndex = Number(e.detail.value);
      const selectedTime = this.timeList[this.selectedTimeIndex];
      this.formData.time = selectedTime.time;
      this.formData.teacherName = selectedTime.teacher;

      this.getSignedNumber();
    },

    handleSubmit() {
      if (!this.isFormValid) {
        uni.showToast({
          title: "请填写完整信息",
          icon: "none",
        });
        return;
      }
      this.btnDisabled = true;
      const train_id = this.periodList[this.selectedPeriodIndex].timeList[
        this.selectedTimeIndex
      ].id;
      elaluationApi
        .TrainCreate({
          student_name: this.formData.name,
          student_mobile: this.formData.mobile,
          train_date: this.formData.date,
          train_time: this.formData.time,
          train_teacher: this.formData.teacherName,
          train_id,
        })
        .then((res) => {
          console.log(res);
          if (res.statusCode === 200) {
            uni.setStorageSync(
              "winterTrainingForm",
              JSON.stringify(this.formData)
            );
            uni.redirectTo({
              url: "/pages/winterTraining/success",
            });
          } else {
            console.log(res.data.error_code);
            uni.showToast({
              title: res.data.error_code,
              icon: "none",
            });
          }
        })
        .finally(() => {
          this.btnDisabled = false;
        });
    },
    handleScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
  },
  onReady() {
    // 监听键盘高度变化
    uni.onKeyboardHeightChange((res) => {
      this.keyboardHeight = res.height;
      if (res.height > 0) {
        // 键盘弹出时，滚动到底部
        setTimeout(() => {
          this.scrollTop = 9999;
        }, 100);
      }
    });
  },
};
</script>

<style lang="scss" scoped>
.winter-training {
  min-height: 100vh;
  background-color: #f7f9fc;
  box-sizing: border-box;
  position: relative;

  .scroll-container {
    height: 100vh;
    box-sizing: border-box;
    padding-bottom: 180rpx;
  }

  .banner-img {
    width: 100%;
    // height: 400rpx;
  }
  .header {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    background-color: #fff;
  }

  .banner {
    width: 100%;
    // height: 400rpx;
    background-color: #eee;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }

  .form-container {
    margin: 20rpx;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 40rpx;

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      //   padding-right: 30rpx;

      .required {
        color: #ff4d4f;
        margin-right: 8rpx;
        width: 20rpx;
      }

      .label {
        width: 200rpx;
        font-size: 28rpx;
        color: #333;
      }

      .input {
        flex: 1;
        height: 80rpx;
        padding: 0 20rpx;
        border: 1rpx solid #eee;
        border-radius: 8rpx;
        font-size: 28rpx;
      }

      .teacher-input {
        line-height: 80rpx;
        color: #333;
        background-color: #f5f5f5;
      }

      .picker-item {
        flex: 1;
      }
      .picker {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 20rpx;
        border: 1rpx solid #eee;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333;
        background-color: #fff;
        position: relative;
      }
      .picker::after {
        content: "";
        width: 16rpx;
        height: 16rpx;
        border-right: 2rpx solid #999;
        border-bottom: 2rpx solid #999;
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
      }
    }
  }

  .footer-placeholder {
    height: 180rpx;
    width: 100%;
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 160rpx;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    z-index: 100;
    .capacity {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 16rpx;
    }
    .num {
      color: #ff4d4f;
    }
    .submit-btn {
      width: 90%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      background-color: #ff6644;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      opacity: 1;
      transition: all 0.3s;
    }
    .submit-btn.disabled {
      background-color: #ccc;
      opacity: 0.6;
      color: rgba(255, 255, 255, 0.9);
    }
    .submit-btn::after {
      border: none;
    }
  }
}
</style>
