import axios from "../http";

function login(data) {
  return axios
    .post("/api/public/match/login", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}

function matchStart(data) {
  return axios
    .post("/api/public/match/start", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}

function macthResult(data) {
  return axios
    .post("/api/public/match/result", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function trainCreate(data) {
  return axios
    .post("/api/public/train/create", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}

function trainList(data) {
  return axios
    .get("/api/public/train/list", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}

function signedNumber(data) {
  return axios
    .get("/api/public/train/signed_number", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function trainConfig(data) {
  return axios
    .get("/api/public/train/config", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
export default {
  async Login(data) {
    return login(data);
  },
  async MatchStart(data) {
    return matchStart(data);
  },
  async MacthResult(data) {
    return macthResult(data);
  },
  async TrainCreate(data) {
    return trainCreate(data);
  },
  async TrainList(data) {
    return trainList(data);
  },
  async SignedNumber(data) {
    return signedNumber(data);
  },
  async TrainConfig(data) {
    return trainConfig(data);
  },
};
