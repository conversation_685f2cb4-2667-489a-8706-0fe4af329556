<template>
  <view class="order_detail">
    <view class="order_input">
      <!-- <view class="uni_input">
        <view class="label">学生姓名：</view>
        <view class="out_input">
          <input placeholder="请输入学生姓名" placeholder-style="color: #CECECE;" v-model="name" />
        </view>
      </view>-->
      <!-- <view class="line"></view> -->

      <view class="uni_input">
        <view class="label">手机号码：</view>
        <view class="out_input">
          <input
            placeholder="请输入手机号码"
            placeholder-style="color: #CECECE;"
            v-model="mobile"
          />
        </view>
      </view>
      <view
        class="line"
        v-if="this.info.course_list && this.info.course_list.length > 0"
      ></view>
      <view
        class="uni_input"
        v-if="this.info.course_list && this.info.course_list.length > 0"
      >
        <view @click="openPicker" class="open-picker"></view>
        <view class="label">选择时间：</view>
        <view class="out_input">
          <input
            placeholder="选择您的上课时间"
            placeholder-style="color: #CECECE;"
            v-model="course_date"
            disabled
          />
        </view>
      </view>
      <view class="line"></view>
      <view class="uni_input">
        <view class="label">验证码：</view>
        <view class="code_input">
          <input
            placeholder="请输入验证码"
            placeholder-style="color: #CECECE;"
            v-model="sms_code"
          />
        </view>

        <view
          class="code_btn"
          style="background: #ff6644"
          v-if="code_blur"
          @click="send_sms"
          >{{ button_text }}</view
        >
        <view
          class="code_btn"
          v-else
          style="background: #cacdd2"
          @click="send_sms"
          >{{ button_text }}</view
        >
      </view>
    </view>
    <view class="order_pic">
      <view class="order_title">
        <view class="title_line"></view>
        <text>订单信息</text>
      </view>
      <view class="course_title">
        <view class="course_logo">网校</view>
        <view class="title">{{ info.merchandise_name }}</view>
      </view>
      <view class="course_detail" v-if="options.mt_name !== '正价课'">
        {{
          info.fixed_time
            ? "开课时间：" +
              moment(info.fixed_time).format("YYYY-MM-DD HH:mm:ss")
            : ""
        }}
      </view>
      <view class="course_detail" v-else>
        {{
          checkedPickerVal
            ? "开课时间：" + moment(checkedPickerVal).format("YYYY-MM-DD")
            : ""
        }}
      </view>
      <view class="teacher_row">
        <!-- <view class="teacher_info">
          <view class="teacher_head"></view>
          <view class="teacher_name">李老师</view>
        </view>-->
        <view class="pic">
          ¥
          <text class="price">
            {{ info.price ? (info.price / 100).toFixed(2) : 0 }}
          </text>
        </view>
      </view>
    </view>
    <view class="pay_container" v-if="this.info.price !== 0">
      <view class="order_title">
        <view class="title_line"></view>
        <text>支付方式</text>
      </view>
      <radio-group @change="radioChange">
        <label v-for="(item, index) in payList" :key="item.value">
          <view class="pay-item" v-if="wechat ? item.value !== 'aliPay' : true">
            <view class="pay-label-container">
              <img class="pay-logo" :src="item.imgUrl" />
              <view class="pay-label-box">
                <view class="pay-label">{{ item.label }}</view>
                <view class="pay-issue">如遇问题，请选择其他支付</view></view
              >
            </view>

            <view>
              <radio
                :value="item.value"
                :checked="index === current"
                color="#FF5655"
                style="border-width: 4rpx"
              /> </view
          ></view>
        </label>
      </radio-group>
    </view>
    <view class="xieyi" @click="goPrivacy">
      <view class="select" @click="isSelect" v-if="select"></view>
      <view class="select_dis" @click="isSelect" v-else></view>
      <view class="xieyi_text">
        <text>我已阅读并同意</text>
        <text style="color: #1eb4ff" @click="go_user">《用户使用协议》</text>和
        <text style="color: #1eb4ff" @click="go_private">《隐私保护协议》</text>
      </view>
    </view>
    <view class="bottom_shop">
      <view
        class="shop_btn"
        @click="reallyPay"
        :style="{ background: pay_color }"
        >{{ payBtnLoading ? "支付中..." : "确认支付" }} ¥{{
          info.price ? (info.price / 100).toFixed(2) : 0
        }}</view
      >
    </view>
    <uni-popup ref="popup" type="bottom" :mask-click="false">
      <view class="select-payment-popop">
        <view class="header-title">
          <view @click="closePopop" class="close-icon">取消</view>
          <view class="confirm-button" @click="confirmDate">确定</view>
        </view>
        <view class="title-container">选择时间</view>
        <view class="date-list-container">
          <view class="date_container">
            <view v-for="(item, index) in this.info.course_list" :key="index">
              <view
                class="date-item"
                :class="{ isSelect: selectNum === index }"
                @click="selectDate(item, index)"
                v-if="item.course_cycle"
              >
                {{ moment(item.fixed_time).format("YYYY-MM-DD") }}
                {{ item.course_cycle }}
              </view>
            </view>
          </view></view
        >
      </view>
    </uni-popup>
    <!-- <custom-picker
      ref="cPicker"
      title="选择时间"
      titleColor="#808080"
      titleFontSize="26rpx"
      cancleFontSize="28rpx"
      confirmFontSize="30rpx"
      confirmFontWeight="500"
      itemFontSize="28rpx"
      @onClose="closePicker"
      @onConfirm="confirmPicker"
      :pickerList="dateList"
    >
    </custom-picker> -->
  </view>
</template>
<script>
!(function(g, d, t, e, v, n, s) {
  if (g.gdt) return;
  v = g.gdt = function() {
    v.tk ? v.tk.apply(v, arguments) : v.queue.push(arguments);
  };
  v.sv = "1.0";
  v.bt = 0;
  v.queue = [];
  n = d.createElement(t);
  n.async = !0;
  n.src = e;
  s = d.getElementsByTagName(t)[0];
  s.parentNode.insertBefore(n, s);
})(
  window,
  document,
  "script",
  "//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js"
);
gdt("init", "1201077621");
import userApi from "../../api/user";
import wx from "jweixin-module";

// import customPicker from "../../components/custom-picker/custom-picker.vue";
export default {
  data() {
    return {
      select: false,
      mobile: "",
      code_blur: false,
      button_text: "获取验证码",
      name: "",
      sms_code: "",
      time: 60,
      info: {},
      options: {},
      orderInfo: {},
      pay_color: "#FF6644",
      code: "",
      open_id: "",
      pay_channel: "",
      wechat: false,
      courseId: "",
      checkedPickerVal: "",
      course_date: "",
      selectNum: "",
      payList: [
        {
          label: "微信支付",
          value: "wechat",
          imgUrl: "../../static/wechat.png",
        },
        {
          label: "支付宝支付",
          value: "aliPay",
          imgUrl: "../../static/aliPay.png",
        },
      ],
      current: 0,
      callWechat: false,
      payListenerTimer: null,
      payBtnLoading: false,
    };
  },
  comments: {
    // customPicker
  },
  watch: {
    mobile(new_str) {
      if (this.isPhoneExp(new_str)) {
        this.code_blur = true;
      } else {
        this.code_blur = false;
      }
    },
  },
  onLoad(option) {
    // uni.setStorageSync("qr_code", "");
    // uni.setStorageSync("start_time", "");
    if (/micromessenger/.test(navigator.userAgent.toLowerCase())) {
      this.wechat = true;
    }
    uni.setStorageSync("callWechat", false);
    this.options = option;
    gdt("track", "PAGE_VIEW", { prouct_id: option.id });
    this.initInfo(option.id);
    if (option.from == "wechat" || option.from == "aliPay") {
      uni.showLoading({
        title: "订单支付中..",
      });
      this.payListener(option.order_number);
    }
  },
  methods: {
    initInfo(id) {
      uni.showLoading({
        title: "加载中",
      });

      userApi.GetCourseInfo({ merchandise_id: id }).then((res) => {
        uni.hideLoading();
        this.info = res.data;
        if (res.data.merchandise_name) {
          uni.setStorageSync("merchandise_name", res.data.merchandise_name);
        }
        if (res.data.id == 0) {
          this.pay_color = "#cacdd2";
        } else {
          this.pay_color = "#FF6644";
        }
        this.open_id = uni.getStorageSync("open_id") || "";
        //微信浏览器且openid存在且金额大于0
        if (
          /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
          this.open_id &&
          this.info.price > 0
        ) {
          this.wechatH5Sign();
        } else {
          if (
            /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
            this.info.price > 0 &&
            !this.options.code
          ) {
            this.wechatDirectUrl();
          } else if (
            /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
            this.info.price > 0 &&
            this.options.code
          ) {
            this.wechatPay();
          }
        }
      });
    },
    wechatDirectUrl() {
      userApi.weChatH5Auth({ url: window.location.href }).then((res) => {
        uni.hideLoading();
        if (res.data) {
          window.location.replace(res.data.data.redirect_url);
        }
      });
    },
    async wechatPay() {
      let res = await userApi.weChatH5Auth({
        code: this.options.code,
        url: window.location.href,
      });
      this.open_id = res.data.data.open_id;
      uni.setStorageSync("open_id", res.data.data.open_id);
      if (!this.open_id) return;
      this.wechatH5Sign();
    },
    async wechatH5Sign() {
      let res = await userApi.weChatH5Sign({ url: window.location.href });
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: res.data.data.appId, // 必填，公众号的唯一标识
        timestamp: res.data.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: res.data.data.noncestr, // 必填，生成签名的随机串
        signature: res.data.data.signature, // 必填，签名
        jsApiList: ["chooseWXPay"], // 必填，需要使用的JS接口列表
      });
    },
    get_adv_source() {
      // 抖音h5链接参数
      // clickid	--> d_click_id
      // account_id
      // ad_id
      // creative_id

      // 腾讯h5链接参数
      // clikd_id --> t_click_id
      // account_id
      // ad_id
      // adcreative_id --> creative_id
      const tx_source = uni.getStorageSync("tx_source");
      const dy_source = uni.getStorageSync("dy_source");
      const click_url = uni.getStorageSync("click_url");
      let obj = {
        d_click_id: "",
        t_click_id: "",
        account_id: "",
        ad_id: "",
        creative_id: "",
        click_url,
      };
      if (tx_source) {
        obj = {
          t_click_id: tx_source.click_id || "",
          account_id: tx_source.account_id || "",
          ad_id: tx_source.ad_id || "",
          creative_id: tx_source.adcreative_id || "",
          click_url,
        };
      }
      if (dy_source) {
        obj = {
          d_click_id: dy_source.clickid || "",
          account_id: dy_source.accountid || "",
          ad_id: dy_source.adid || "",
          creative_id: dy_source.creativeid || "",
          project_id: dy_source.projectid || "",
          click_url,
        };
      }
      return obj;
    },
    h5_wechat_pay() {
      if (/micromessenger/.test(navigator.userAgent.toLowerCase())) {
        var _self = this;
        let url = window.location.href;
        uni.hideLoading();
        const source_params = this.get_adv_source();
        let trackId = uni.getStorageSync("trackId") || "";
        const { old_course } = this.options;
        let from = {
          mobile: this.mobile,
          sms_code: this.sms_code,
          source: this.options.source ? this.options.source : "",
          course_id: this.courseId || this.info.id,
          open_id: this.open_id,
          trade_type: "JSAPI",
          tw_callback: uni.getStorageSync("tw_callback"),
          click_id: uni.getStorageSync("clickid"),
          teacher_id:
            this.options.teacher_id != "undefined"
              ? this.options.teacher_id
              : "",
          po_id: this.options.po_id ? this.options.po_id : "",
          ...source_params,
          trackId,
          old_course,
          recommend_id: this.options.recommend_id || "",
          is_train: this.options.is_train || "",
        };
        userApi
          .CreateOrder(from)
          .then((res) => {
            this.payBtnLoading = false;
            this.pay_color = "#FF6644";
            wx.chooseWXPay({
              appId: res.data.appId,
              timestamp: res.data.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
              nonceStr: res.data.nonceStr, // 支付签名随机串，不长于 32 位
              package: res.data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
              signType: res.data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
              paySign: res.data.paySign, // 支付签名
              success: function() {
                /* eslint-disable */
                uni.showLoading({
                  title: "加载中",
                });

                let clickid = uni.getStorageSync("clickid");
                if (clickid) {
                  _self.initClick(clickid, res.data.qr_code);
                } else {
                  // setTimeout(() => {
                  //   uni.hideLoading();
                  //   let merchandise_name = uni.getStorageSync("merchandise_name");

                  //   uni.navigateTo({
                  //     url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`,
                  //   });
                  // }, 1500);
                  _self.wechatPayListener(
                    res.data.order_number,
                    _self.info.price,
                    url
                  );
                }

                //状态
                return;
                /* eslint-enable */
                // setTimeout
              },
              cancel: function(res) {
                uni.hideLoading();
                uni.showToast({
                  title: "支付失败",
                  duration: 1000,
                  icon: "none",
                });
                /* eslint-disable */
                // _self.$refs.confirm_dialog.open();
                /* eslint-enable */
              },
              fail: function(res) {
                /* eslint-disable */
                // console.log(res);
                /* eslint-enable */
              },
            });
          })
          .catch(() => {
            this.payBtnLoading = false;
            this.pay_color = "#FF6644";
          });
      } else {
      }
    },

    isSelect() {
      this.select = !this.select;
    },
    send_sms: function() {
      if (
        (this.button_text === "获取验证码" ||
          this.button_text === "重新获取") &&
        this.mobile
      ) {
        if (this.isPhoneExp(this.mobile)) {
          userApi.GetSendSMS({ mobile: this.mobile }).then((res) => {
            // console.log(res);
            if (res.statusCode == 200) {
              this.time = 60;
              this.time_down();
              this.code_blur = false;
            }
          });
        } else {
          uni.showToast({
            title: "请输入正确的手机号",
            duration: 1000,
            icon: "none",
          });
        }
      }
    },
    time_down() {
      if (this.time > 0) {
        this.time--;
        this.button_text = this.time + "s后重发";
        setTimeout(this.time_down, 1000);
      } else {
        this.time = 0;
        this.button_text = "重新获取";
        this.code_blur = true;
      }
    },
    goPrivacy() {},
    mobileChange(val) {
      console.log(val);
    },
    isPhoneExp(str) {
      let pattern = /^1(3|4|5|6|7|8|9)\d{9}$/;
      return pattern.test(str);
    },
    go_private() {
      uni.navigateTo({
        url: `/pages/privateAgreement/privateAgreement`,
      });
    },
    go_user() {
      uni.navigateTo({
        url: `/pages/userAgreement/userAgreement`,
      });
    },
    Topay() {
      uni.navigateTo({
        url: `/pages/payment/pay`,
      });
    },
    reallyPay() {
      const { price, id } = this.info;
      if (id == 0) {
        uni.showToast({
          title: "课程id不存在,请联系客服",
          duration: 2000,
          icon: "none",
        });
        return;
      }
      if (id == "172") {
        //添加自定义属性，如:点击“开始阅读”按钮
        hina.track("classes_pay_click", {
          classes_type: "聂道少儿围棋",
        });
        console.log("hina :>> ", hina);
      }

      if (this.mobile && this.sms_code) {
        if (!this.select) {
          uni.showToast({
            title: "请勾选协议",
            duration: 1000,
            icon: "none",
          });
          return;
        }
        uni.setStorageSync(
          "start_time",
          this.info.fixed_time
            ? this.moment(this.info.fixed_time).format("YYYY-MM-DD HH:mm:ss")
            : ""
        );
        let clickid = uni.getStorageSync("clickid");
        if (clickid) {
          userApi
            .courseClick({ click_id: clickid, event_type: "shopping" })
            .then((res) => {});
        }
        let bdVid = uni.getStorageSync("bdVid");
        if (bdVid) {
          let url = window.location.href;
          userApi
            .toBaidu({ logidUrl: `${url}&bd_vid=${bdVid}`, newType: 3 })
            .then((res) => {});
        }
        if (this.payBtnLoading) {
          return;
        }
        this.payBtnLoading = true;
        this.pay_color = "#cacdd2";
        if (
          /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
          this.info.price > 0
        ) {
          // 拉起支付
          this.h5_wechat_pay();
        } else {
          uni.showLoading({
            title: "加载中",
          });
          const source_params = this.get_adv_source();
          let trackId = uni.getStorageSync("trackId") || "";
          const { old_course } = this.options;
          let from = {
            mobile: this.mobile,
            sms_code: this.sms_code,
            source: this.options.source ? this.options.source : "",
            course_id: this.courseId || this.info.id,
            tw_callback: uni.getStorageSync("tw_callback"),
            click_id: clickid,
            pay_channel: this.pay_channel,
            teacher_id:
              this.options.teacher_id != "undefined"
                ? this.options.teacher_id
                : "",
            ali_callback:
              this.pay_channel === "aliPay"
                ? window.location.href + "&from=aliPay&order_number="
                : "",
            po_id: this.options.po_id ? this.options.po_id : "",
            ...source_params,
            trackId,
            old_course,
            recommend_id: this.options.recommend_id || "",
            is_train: this.options.is_train || "",
          };

          userApi
            .CreateOrder(from)
            .then((res) => {
              uni.hideLoading();
              this.payBtnLoading = false;
              this.pay_color = "#FF6644";
              const merchandise_name = encodeURIComponent(
                this.info.merchandise_name
              );
              if (res.statusCode == 200) {
                if (this.info.price == 0) {
                  uni.navigateTo({
                    url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`,
                  });
                } else {
                  if (this.pay_channel === "aliPay") {
                    if (res.data) {
                      // uni.showLoading({
                      //   title: "订单支付中..",
                      // });
                      this.payListener(res.data.order_number);
                      // const merchandise_name = this.info.merchandise_name;
                      const origin = window.location.origin;
                      window.location.href = `alipays://platformapi/startapp?saId=10000007&qrcode=${origin}/pages/order/alipay?pay_url=${res.data.pay_url}_order_number_${res.data.order_number}_merchandise_name_${merchandise_name}`;
                    }
                    // window.location.href = res.data.pay_url;
                  } else {
                    window.location.replace(
                      res.data.mweb_url +
                        "&redirect_url=" +
                        encodeURIComponent(
                          window.location.href +
                            "&from=wechat&order_number=" +
                            res.data.order_number
                        )
                    );
                  }
                }
              } else {
                this.payBtnLoading = false;
                this.pay_color = "#FF6644";
              }
            })
            .catch(() => {
              this.payBtnLoading = false;
              this.pay_color = "#FF6644";
            });
        }
      } else {
        uni.showToast({
          title: "信息不能为空，请正确填写。",
          duration: 2000,
          icon: "none",
        });
      }
    },
    radioChange(check) {
      this.pay_channel = check.detail.value;
    },
    payListener(order_number) {
      this.payBtnLoading = false;
      this.pay_color = "#FF6644";
      if (this.payListenerTimer) {
        clearInterval(this.payListenerTimer);
      }
      this.payListenerTimer = setInterval(() => {
        userApi.GetPayResult({ order_number }).then((res) => {
          if (res.data.pay_status != "in_pay") {
            clearInterval(this.payListenerTimer);
          } else {
            uni.hideLoading();
          }
          if (res.data.pay_status == "no_pay") {
            uni.showToast({
              title: "支付失败",
              duration: 1000,
              icon: "none",
            });
          }
          if (res.data.pay_status == "is_pay") {
            gdt("track", "COMPLETE_ORDER", { value: this.info.price });
            uni.setStorageSync(
              "start_time",
              this.info.fixed_time
                ? this.moment(this.info.fixed_time).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                : ""
            );
            let bdVid = uni.getStorageSync("bdVid");
            if (bdVid) {
              let url = window.location.href;
              userApi
                .toBaidu({ logidUrl: `${url}&bd_vid=${bdVid}`, newType: 10 })
                .then((res) => {});
            }
            // let trackId = uni.getStorageSync("trackId");
            // if (trackId) {
            //   userApi.toRed({ trackId, type: 3 }).then((res) => {});
            // }
            //腾讯广告引流付费统计
            uni.showToast({
              title: "支付成功",
              duration: 1000,
              icon: "success",
            });
            const merchandise_name = encodeURIComponent(
              this.info.merchandise_name
            );
            uni.navigateTo({
              url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`,
            });
          }
        });
      }, 1000);
    },
    wechatPayListener(order_number, price, url) {
      this.payBtnLoading = false;
      this.pay_color = "#FF6644";
      uni.showLoading({
        title: "加载中...",
      });
      let time = 0;
      let t = setInterval(() => {
        time += 1;
        if (time == 6) {
          clearInterval(t);
        }
        userApi.GetPayResult({ order_number }).then((res) => {
          if (res.data.pay_status == "is_pay") {
            let callWechat = uni.getStorageSync("callWechat");

            if (!callWechat) {
              uni.setStorageSync("callWechat", true);
              //腾讯广告引流付费统计
              gdt("track", "COMPLETE_ORDER", { value: price });
              let bdVid = uni.getStorageSync("bdVid");
              if (bdVid) {
                userApi
                  .toBaidu({ logidUrl: `${url}&bd_vid=${bdVid}`, newType: 10 })
                  .then((res) => {});
              }
              // let trackId = uni.getStorageSync("trackId");
              // if (trackId) {
              //   userApi.toRed({ trackId, type: 3 }).then((res) => {});
              // }
            }
            uni.hideLoading();
            let merchandise_name = encodeURIComponent(
              uni.getStorageSync("merchandise_name")
            );
            clearInterval(t);
            uni.navigateTo({
              url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`,
            });
          }
        });
      }, 1000);
    },
    initClick(clickid, qr_code) {
      userApi
        .courseClick({ click_id: clickid, event_type: "active_pay" })
        .then((res) => {
          console.log(res);
          let merchandise_name = encodeURIComponent(
            uni.getStorageSync("merchandise_name")
          );
          uni.navigateTo({
            url: `/pages/success/success?qr_code=${qr_code}&merchandise_name=${merchandise_name}`,
          });
        });
    },
    // closePicker() {
    //   this.$refs.cPicker._close();
    // },
    // //打开
    openPicker() {
      // this.$refs.cPicker._open();
      this.$refs.popup.open();
    },
    // //确定：接收子组件传来的参数
    // confirmPicker(checkedValue) {
    //   this.checkedPickerVal = checkedValue;
    // },
    closePopop() {
      this.$refs.popup.close();
    },
    confirmDate() {
      if (this.selectNum || this.selectNum === 0) {
        this.checkedPickerVal = this.moment(
          this.info.course_list[this.selectNum].fixed_time
        ).format("YYYY-MM-DD");
        this.course_date =
          this.moment(this.info.course_list[this.selectNum].fixed_time).format(
            "MM-DD"
          ) +
          " " +
          this.info.course_list[this.selectNum].course_cycle;
        this.courseId = this.info.course_list[this.selectNum].id;
      }
      this.closePopop();
    },
    selectDate(item, index) {
      this.selectNum = index;
    },
  },
};
</script>
<style scoped>
.order_detail {
  width: 100vw;
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 260rpx;
  box-sizing: border-box;
}
.order_input {
  background: #fff;
  margin-top: 20rpx;
  min-height: 180rpx;
  padding: 32rpx;
}
.order_pic {
  background: #fff;
  margin-top: 20rpx;
  min-height: 240rpx;
  padding: 32rpx;
  position: relative;
}
.pay_container {
  background: #fff;
  margin-top: 20rpx;
  max-height: 260rpx;
  padding: 32rpx;
  position: relative;
}
.uni_input {
  display: flex;
  position: relative;
}
.code_input {
  width: 270rpx;
}
.out_input {
  width: 450rpx;
}
.open-picker {
  position: absolute;
  top: -12rpx;
  left: 0;
  width: 100%;
  height: 70rpx;
  z-index: 999;
}
.label {
  width: 180rpx;
  font-size: 30rpx;
}
.line {
  background: #f1f1f1;
  height: 2rpx;
  margin: 30rpx 0;
}
.code_btn {
  width: 200rpx;
  height: 68rpx;
  border-radius: 32rpx;
  background: #cacdd2;
  font-size: 30rpx;
  text-align: center;
  line-height: 68rpx;
  color: #fff;
}
.order_title {
  display: flex;
  height: 28rpx;
  align-items: center;
  margin-bottom: 32rpx;
}
.title_line {
  width: 6rpx;
  height: 28rpx;
  background: #ff6644;
  border-radius: 4rpx;
  margin-right: 12rpx;
}
.course_title {
  display: flex;
}
.course_logo {
  width: 80rpx;
  height: 40rpx;
  background-image: linear-gradient(180deg, #ffc80a 0%, #ffa62a 100%);
  border-radius: 6px;
  font-size: 26rpx;
  text-align: center;
  line-height: 40rpx;
  color: #fff;
  margin-right: 16rpx;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
}
.course_detail {
  font-size: 30rpx;
  color: #999999;
  margin: 18rpx 0 32rpx 0;
}
.teacher_row {
  display: flex;
}
.teacher_head {
  width: 52rpx;
  height: 52rpx;
  background: #ccc;
  border-radius: 36rpx;
}
.teacher_name {
  font-size: 22rpx;
  color: #999999;
}
.pic {
  color: #ff6644;
  margin: 0 40rpx;
  position: absolute;
  right: 0;
}
.price {
  font-size: 56rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
.xieyi {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}
.select_dis {
  width: 26rpx;
  height: 26rpx;
  border-radius: 15rpx;
  border: 3rpx solid #d2d2d5;
  margin-right: 12rpx;
}
.select {
  width: 30rpx;
  height: 30rpx;
  border-radius: 15rpx;
  background: url("../../static/select.png") no-repeat;
  background-size: 100%;
  margin-right: 12rpx;
}
.xieyi_text {
  font-size: 24rpx;
  color: #c2c2c6;
}
.bottom_shop {
  width: 100vw;
  height: 196rpx;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  box-shadow: 0 2rpx 30rpx 0 rgba(45, 45, 45, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop_btn {
  width: 654rpx;
  height: 88rpx;
  border-radius: 44rpx;
  background: #ff6644;
  /* box-shadow: 0 6rpx 30rpx 0 rgba(255, 102, 68, 0.5); */
  text-align: center;
  line-height: 88rpx;
  color: #fff;
  font-size: 34rpx;
}
.pay-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  background: #fff;
  margin-bottom: 32rpx;
}
.pay-label-container {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: flex-end;
}
.pay-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 16rpx;
}
.pay-label-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  align-items: flex-start;
}
.pay-label {
  font-size: 32rpx;
  line-height: 40rpx;
  font-weight: bold;
  color: #333333;
}
.pay-issue {
  color: #abadb0;
  font-size: 24rpx;
  line-height: 32rpx;
}
.select-payment-popop {
  background: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 700rpx;
  box-sizing: border-box;
  border-radius: 50rpx 50rpx 0 0;
  justify-content: space-between;
  margin: 0 20rpx;
  padding: 45rpx;
}
.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 22rpx;
  font-size: 30rpx;
  color: #444444;
  width: 100%;
  font-weight: 500;
  box-sizing: border-box;
  /* border-bottom: 2rpx solid #f1f1f1; */
}
.confirm-button {
  color: #fa6400;
}

.date_container {
  width: 100%;
}
.title-container {
  height: 40rpx;
  font-size: 26rpx;
  line-height: 40rpx;
  text-align: center;
  width: 100%;
  color: rgba(0, 0, 0, 0.5);
}
.date-list-container {
  flex: 1;
  width: 100%;
  overflow-y: scroll;
}
.date-item {
  width: 100%;
  background: #f8f8f8;
  border-radius: 20rpx;
  box-sizing: border-box;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  color: #444444;
  font-size: 30rpx;
  margin: 20rpx 0;
}
.isSelect {
  background: rgba(255, 102, 68, 0.1);
  border: 2rpx solid #ff6644;
  box-sizing: border-box;
  color: #ff6644;
}
</style>
