import Vue from "vue";
const utils = {
  // 手机号格式是否正确
  isMobilePhone(value) {
    const reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
    if (reg.test(value)) {
      return true;
    }
    return false;
  },
  isPassword(value) {
    const reg = /(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~!@#$%^&*()]{8,100}$/;
    if (reg.test(value)) {
      return true;
    }
    return false;
  }
};

Vue.prototype.$utils = utils;

export default utils;
