<template>
  <view class="choice-issue-list">
    <view class="issue-wrap">
      <view class="issue-title">
        <!-- #ifdef APP-PLUS -->
        <image
          class="voice"
          @click="change_choice"
          :style="{
            'background-image': voice_status
              ? `url(${require('../../static/cwc/exerciseDetail/has_voice.png')})`
              : `url(${require('../../static/cwc/exerciseDetail/no_voice.png')})`,
            'background-size': '100% 100%'
          }"
        >
        </image>
        <!-- #endif -->
        {{ exercise_detail.name }}</view
      >
      <view
        class="choice-issue"
        v-if="exercise_detail.type !== 'play_on_board'"
      >
        <view
          v-for="(item, index) in option_list"
          :key="index"
          @click="check_answer(item)"
          :class="[
            'choice-issue-option',
            user_answer === ''
              ? 'choice-issue-option-default'
              : answer_result === true && user_answer === Object.keys(item)[0]
              ? 'choice-issue-option-true'
              : answer_result === false && user_answer === Object.keys(item)[0]
              ? 'choice-issue-option-false'
              : answer_result === false &&
                issue_answer === Object.keys(item)[0] &&
                game_end === true
              ? 'choice-issue-option-true'
              : show_answer === true &&
                issue_answer === Object.keys(item)[0] &&
                game_end === true
              ? 'choice-issue-option-true'
              : 'choice-issue-option-default'
          ]"
        >
          <i class="option-icon">
            <span v-if="answer_result === ''">
              {{ Object.keys(item)[0] }}
            </span>
            <span
              v-else-if="
                answer_result === true && user_answer !== Object.keys(item)[0]
              "
            >
              {{ Object.keys(item)[0] }}
            </span>
            <!-- <span
          v-if="answer_result === true && issue_answer === Object.keys(item)[0]"
        >
        </span> -->
            <!-- <span v-else-if="game_end === false"> -->
            <span
              v-else-if="
                answer_result === false &&
                  user_answer !== Object.keys(item)[0] &&
                  game_end === false
              "
            >
              {{ Object.keys(item)[0] }}
            </span>
            <span
              v-else-if="
                answer_result === false &&
                  issue_answer === Object.keys(item)[0] &&
                  game_end === false
              "
            >
              {{ Object.keys(item)[0] }}
            </span>
            <!-- </span> -->

            <!-- <span
            v-if="
              answer_result === false &&
              user_answer !== Object.keys(item)[0] 
            "
          >
            {{ Object.keys(item)[0] }}
          </span> -->
            <span
              v-else-if="
                answer_result === false &&
                  issue_answer !== Object.keys(item)[0] &&
                  user_answer !== Object.keys(item)[0] &&
                  game_end === true
              "
            >
              {{ Object.keys(item)[0] }}
            </span>

            <span
              v-else-if="
                answer_result === true && issue_answer !== Object.keys(item)[0]
              "
            >
              {{ Object.keys(item)[0] }}
            </span>

            <!-- <span
          v-else-if="
            (answer_result === true && user_answer !== Object.keys(item)[0]) ||
            (answer_result === false && user_answer !== Object.keys(item)[0])
          "
        >
          {{ Object.keys(item)[0] }}
        </span> -->

            <!-- <span
          v-if="
            answer_result === '' ||
            (answer_result === true && user_answer !== Object.keys(item)[0]) ||
            (answer_result === false &&
              user_answer !== Object.keys(item)[0] &&
              answer_result === false &&
              user_answer === Object.keys(item)[0])
          "
        >
          {{ Object.keys(item)[0] }}
        </span> -->
          </i>
          <view class="option-text">{{ Object.values(item)[0] }}</view>
        </view>
      </view>
      <view v-else>     
        <p class="tips" v-if="result === ''">请在棋盘上落子</p>
        <p class="tips" v-if="result === true" style="color: #34cc67;">
          答对了，你真棒！
        </p>
        <p class="tips" v-if="result === false" style="color: #ff5f69;">
          答错了，继续加油！
        </p>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "choice-issue",
  data() {
    return {
      voice_status: uni.getStorageSync("voice_status")
    };
  },
  props: {
    issue_answer: {
      //题目答案
      type: String,
      default: ""
    },
    option_list: {
      default: ""
    },
    show_answer: {
      //是否展示答案
      type: Boolean
    },
    game_end: {
      //练习结束
      type: Boolean,
      default: false
    },
    is_redo: {
      type: Boolean,
      default: ""
    },
    user_answer: {
      default: ""
    },
    answer_result: {
      default: ""
    },
    exercise_detail: {},
    result: {
      default: ""
    }
  },
  watch: {
    is_redo: {
      handler: function(data) {
        if (data === true) {
          this.answer_result = "";
          this.user_answer = "";
          this.$emit("reset");
        }
      },
      deep: true
    }
    // user_answer: {
    //   handler: function (data) {
    //     console.log(data)
    //     this.user_answer = data.answer_choice
    //     this.answer_result = data.answer_result
    //     // this.$emit("reset")
    //   },
    //   deep: true
    // }
  },
  methods: {
    check_answer: function(choice) {
      if (this.answer_result === "") {
        if (choice) {
          if (Object.keys(choice)[0] === this.issue_answer) {
            //this.answer_result = true
            this.$emit("answerRight", Object.keys(choice)[0]);
          } else {
            //this.answer_result = false
            this.$emit("answerWrong", Object.keys(choice)[0]);
          }
        }
      }
    },
    change_choice: function() {
      this.voice_status = !this.voice_status;
      this.$emit("changeVoiceStatus");
      uni.setStorageSync("voice_status", this.voice_status);
    }
  }
};
</script>
<style lang="less" scoped>
.choice-issue-list {
  width: 100%;
  .issue-wrap {
    width: 100%;
    padding: 24rpx 32rpx 0 32rpx;
    box-sizing: border-box;
    .issue-title {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 50rpx;
      margin-bottom: 24rpx;
      text-align: left;
      .voice {
        width: 40rpx;
        height: 40rpx;
        margin: 10px 10rpx -8rpx 0;
      }
    }
    .tips {
      font-size: 28rpx;
      color: #1eb4ff;
      text-align: left;
    }
    .choice-issue {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .choice-issue-option {
        line-height: 88rpx;
        width: 319rpx;
        height: 88rpx;
        border: 2rpx solid #e5e5e5;
        border-radius: 44rpx;
        box-sizing: border-box;
        text-align: center;
        position: relative;
        margin-bottom: 16rpx;
        .option-icon {
          width: 72rpx;
          height: 72rpx;
          line-height: 72rpx;
          border-radius: 36rpx;
          position: absolute;
          top: 50%;
          margin-top: -36rpx;
          left: 8rpx;
          font-style: normal;
        }
        .option-text {
          font-family: PingFangSC-Semibold;
          font-size: 32rpx;
          letter-spacing: 0;
          line-height: 88rpx;
        }
      }
      .choice-issue-option-false {
        background-color: #ff6667;
        border: none;
        .option-text {
          color: #ffffff;
        }
        .option-icon {
          background: url(~@/static/cwc/exerciseDetail/close.png) no-repeat;
          background-size: contain;
          background-color: #ffffff;
        }
      }
      .choice-issue-option-true {
        background-color: #54d773;
        .option-text {
          color: #ffffff;
        }
        .option-icon {
          background: url(~@/static/cwc/exerciseDetail/check.png) no-repeat;
          background-size: contain;
          background-color: #54d773;
        }
      }
      .choice-issue-option-default {
        background-color: #ffffff;
        .option-text {
          color: #333333;
        }
        .option-icon {
          background-color: #f1f1f1;
        }
      }
    }
  }
  @media screen and (max-width: 2500px) and (min-width: 500px) {
    .issue-wrap {
      width: 100%;
      padding: 22px 22rpx 0 22rpx;
      box-sizing: border-box;
      .issue-title {
        font-family: PingFangSC-Semibold;
        font-size: 24rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 40rpx;
        margin-bottom: 14rpx;
        .voice {
          width: 30rpx;
          height: 30rpx;
          margin: 10px 10rpx -7rpx 0;
        }
      }
      .tips {
        font-size: 22rpx;
        color: #1eb4ff;
      }
      .choice-issue {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .choice-issue-option {
          line-height: 55rpx;
          width: 48%;
          height: 55rpx;
          border: 2rpx solid #e5e5e5;
          border-radius: 44rpx;
          box-sizing: border-box;
          text-align: center;
          position: relative;
          margin-bottom: 14rpx;
          .option-icon {
            width: 42rpx;
            height: 42rpx;
            line-height: 42rpx;
            border-radius: 21rpx;
            position: absolute;
            top: 50%;
            margin-top: -21rpx;
            left: 6rpx;
            font-style: normal;
            span {
              font-size: 24rpx;
            }
          }
          .option-text {
            font-family: PingFangSC-Semibold;
            font-size: 24rpx;
            letter-spacing: 0;
            line-height: 53rpx;
            padding-left: 30rpx;
          }
        }
        .choice-issue-option-false {
          background-color: #ff6667;
          .option-text {
            color: #ffffff;
          }
          .option-icon {
            background: url(~@/static/cwc/exerciseDetail/close.png) no-repeat;
            background-size: contain;
            background-color: #ffffff;
          }
        }
        .choice-issue-option-true {
          background-color: #54d773;
          .option-text {
            color: #ffffff;
          }
          .option-icon {
            background: url(~@/static/cwc/exerciseDetail/check.png) no-repeat;
            background-size: contain;
            background-color: #54d773;
          }
        }
        .choice-issue-option-default {
          background-color: #ffffff;
          .option-text {
            color: #333333;
          }
          .option-icon {
            background-color: #f1f1f1;
          }
        }
      }
    }
  }
}
</style>
