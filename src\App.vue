<script>
import config from "./config.js";
export default {
  onLaunch: function() {
    // #ifdef APP-PLUS
    // plus.screen.lockOrientation("portrait-primary");
    // // #endif
    // if (uni.getStorageSync("user_token")) {
    //   this.$store.commit("setPersonalInformation", "");
    //   this.$store.dispatch("getPersonalInformation");
    // }
    // uni.request({
    //   url: config.baseUrl + "/api/v1/app-version",
    //   header: {
    //     "content-type": "application/json"
    //   },
    //   success: (res) => {
    //     //#ifdef APP-PLUS
    //     plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
    //       if (
    //         parseInt(
    //           (res.data.app_version.replace(/\./g, "") * 1000 + "").substr(0, 5)
    //         ) >
    //         parseInt(
    //           (wgtinfo.version.replace(/\./g, "") * 1000 + "").substr(0, 5)
    //         )
    //       ) {
    //         this.show_modal(res.data);
    //       }
    //     });
    //     //#endif
    //   }
    // });
  },
  methods: {
    //#ifdef APP-PLUS
    show_modal(data) {
      uni.showModal({
        title: "提示",
        content: "版本有更新",
        showCancel: false,
        confirmText: "去下载",
        success: function(res) {
          if (res.confirm) {
            // 如果是ios跳转到Appstore
            if (uni.getSystemInfoSync().platform === "ios") {
              plus.runtime.launchApplication(
                {
                  action: data.ios_url,
                },
                function(e) {
                  console.log(
                    "Open system default browser failed: " + e.message
                  );
                }
              );
            } else if (uni.getSystemInfoSync().platform === "android") {
              if (uni.getSystemInfoSync().brand === "HUAWEI") {
                plus.runtime.openURL(encodeURI(data.huawei_url));
              } else {
                // 如果是andorid浏览器打开网址
                plus.runtime.openURL(encodeURI(data.android_url));
              }
            }
          }
        },
      });
    },
    //#endif
  },
  onShow: function() {
    // console.log("App Show");
  },
  onHide: function() {
    //console.log("App Hide")
  },
};
</script>
<style>
@import "./common/uni.css";
@import "./common/font/font.css";
/*每个页面公共css */
.safe_bottom {
  /* #ifdef H5 */
  height: 168rpx !important;
  box-sizing: border-box !important;
  /* #endif */
  bottom: 0;
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
}
.tabbar-bg {
  width: 100%;
  height: 120rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
}
</style>
