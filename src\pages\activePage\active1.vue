<template>
  <div class="active1">
    <div class="bg" style="height: 650rpx;">
      <img src="../../../public/active/bg1.jpg" alt="">
    </div>
    <div class="bg" style="height: 500rpx;">
      <img src="../../../public/active/bg2.jpg" alt="">
    </div>
    <div class="bg" style="height: 850rpx;">
      <!-- <img style="position: absolute; z-index: -1;" src="../../../public/active/bg3.jpg" alt=""> -->
      <div class="form">
        <form>
          <view class="uni-form-item uni-column">
            <view class="title"><span class="required">*&nbsp;</span>手机号：</view>
            <input class="uni-input" v-model="formModel.mobile" @blur="handleMobileBlur" type="number" placeholder="请输入手机号" />
          </view>
          <view class="uni-form-item uni-column">
            <view class="title"><span class="required">*&nbsp;</span>验证码：</view>
            <input 
              class="uni-input" 
              maxlength="6" 
              style="width: 250rpx;" 
              v-model="formModel.sms_code" 
              type="number" 
              placeholder="请输入验证码" 
            />
            <view
              class="code_btn"
              style="background: #ff6644"
              v-if="code_blur"
              @click="send_sms"
              >{{ button_text }}</view
            >
            <view
              class="code_btn"
              v-else
              :style="{ background: isMobile ? '#ff6644' : '#cacdd2' }"
              @click="send_sms"
              >{{ button_text }}</view
            >
          </view>
          <view class="uni-form-item uni-column">
            <view class="title"><span class="required">*&nbsp;</span>姓&nbsp;&nbsp;&nbsp;名：</view>
            <input class="uni-input" v-model.trim="formModel.name" placeholder="请输入姓名" />
          </view>
          <view class="uni-form-item uni-column">
            <view class="title"><span class="required">*&nbsp;</span>等&nbsp;&nbsp;&nbsp;级：</view>
            <picker :disabled="isDisabledLevel" @change="bindPickerChange" :value="index" :range="levelList" range-key="level">
              <view class="uni-input" style="font-size: 32rpx;">
                <!--  :style="{color: index === 0 ? '#8c7474' : '' }" -->
                <span v-if="levelList[index]">{{ levelList[index].level }}</span>
                <div v-else>
                  <span style="font-size: 30rpx; color: #8c7474;">请选择等级</span>
                  <!-- <span class="triangle-down"></span> -->
                </div>
              </view>
            </picker>
          </view>

          <view class="uni-form-item uni-column">
            <view class="title"><span class="required">*&nbsp;</span>邮寄地址：</view>
            <input class="uni-input" v-model.trim="formModel.address" placeholder="请输入邮寄地址" />
          </view>
          <view class="uni-form-item uni-column">
            <view class="title"><span class="required">*&nbsp;</span>在读年级：</view>
            <input class="uni-input" v-model.trim="formModel.class_name" placeholder="请输入在读年级" />
          </view>
          <view class="uni-form-item uni-column" style="display: block;">
            <view class="title"><span class="required">*&nbsp;</span>参赛场次：</view>
            <div class="event-list">
              <div
                :class="['event-item', formModel.event_ids.includes(item.id) ? 'checked' : '']" 
                v-for="item in eventList" :key="item.id" @click="handleEventClick(item)"
              >
                <div class="checked-icon"></div>
                <p v-for="txt in item.name" :key="txt">
                  {{ txt }}
                </p>
              </div>
            </div>
          </view>
        </form>
      </div>
    </div>
    <div class="footer">
      <div class="footer-mian">
        <template v-if="this.formModel.sign_type === 'sign'">
          <button style="width: 350rpx; border-radius: 50rpx;" type="warn" @click="save">确认报名</button>
        </template>
        <template v-else>
          <div style="margin-right: 80rpx;">
            <span class="unit">￥</span>
            <span class="price">{{ (totalPrice / 100).toFixed(2) }}</span>
          </div>
          <button style="width: 350rpx; border-radius: 50rpx; margin: 0;" type="warn" @click="payMoney">立即支付</button>
        </template>
      </div>
    </div>
    <div class="pay-channel-modal" v-if="payChannelModal">
      <div class="pay-channel-modal-wrap">
        <view class="pay_container">
          <view class="order_title">
            <view class="title_line"></view>
            <text>确认支付</text>
          </view>
          <div class="close" @click="handleChannelClose">×</div>
          <radio-group @change="radioChange">
            <label v-for="item in payList" :key="item.value">
              <view class="pay-item" v-if="wechat ? item.value !== 'aliPay' : true">
                <view class="pay-label-container">
                  <img class="pay-logo" :src="item.imgUrl" />
                  <view class="pay-label-box">
                    <view class="pay-label">{{ item.label }}</view>
                    <view class="pay-issue">银行卡/信用卡都可以使用</view>
                  </view>
                </view>
                <view>
                  <radio
                    :value="item.value"
                    :checked="item.value === pay_channel"
                    color="#FF5655"
                    style="border-width: 4rpx"
                  /> </view
              ></view>
            </label>
          </radio-group>
          <button style="width: 350rpx; border-radius: 50rpx; margin: 30rpx auto;" type="warn" @click="save(formModel.pay_method)">{{ payBtnLoading ? "支付中..." : "立即支付" }}</button>
        </view>
      </div>
    </div>
  </div>
</template>

<script>
import activeApi from "../../api/active1";
import userApi from "../../api/user";
// import vconsole from "vconsole";
import wx from "jweixin-module";
export default {
  components: {},
  data() {
    return {
      isDisabledLevel: true,
      index: "",
      levelList: [],
      resLevels: "",
      standbyLevels: [
        // {
        //   level: "请选择等级"
        // },
        {
          level: "N1"
        },
        {
          level: "N2"
        },
        {
          level: "N3"
        },
        {
          level: "N4"
        },
        {
          level: "N5"
        },
        {
          level: "N6"
        },
        {
          level: "N7"
        },
        {
          level: "N8"
        }
      ],
      isMobile: false,
      payBtnLoading: false,
      pay_channel: "",
      wechat: false,
      payChannelModal: false,
      payList: [
        {
          label: "微信支付",
          value: "wechat",
          imgUrl: "../../static/wechat.png",
        },
        {
          label: "支付宝支付",
          value: "aliPay",
          imgUrl: "../../static/aliPay.png",
        },
      ],
      payListenerTimer: null,
      eventList: [],
      totalPrice: 0,
      options: {},
      code_blur: false,
      button_text: "获取验证码",
      formModel: {
        mobile: "",
        name: "",
        sms_code: "",
        open_id: "",
        address: "",
        class_name: "",
        event_ids: [],
        pay_method: "",
        trade_type: "",
        ali_callback: "",
        sign_type: "sign",
      }
    }
  },
  computed: {},
  methods: {
    wechatDirectUrl() {
      userApi.weChatH5Auth({ url: window.location.href }).then((res) => {
        uni.hideLoading();
        console.log(res.data);
        if (res.data) {
          window.location.replace(res.data.data.redirect_url);
        }
      });
    },
    isPhoneExp(str) {
      let pattern = /^1(3|4|5|6|7|8|9)\d{9}$/;
      return pattern.test(str);
    },
    async wechatPay() {
      let res = await userApi.weChatH5Auth({
        code: this.options.code,
        url: window.location.href,
      });
      this.formModel.open_id = res.data.data.open_id;
      if (!this.formModel.open_id) return;
      this.wechatH5Sign();
    },
    async wechatH5Sign() {
      let res = await userApi.weChatH5Sign({ url: window.location.href });
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: res.data.data.appId, // 必填，公众号的唯一标识
        timestamp: res.data.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: res.data.data.noncestr, // 必填，生成签名的随机串
        signature: res.data.data.signature, // 必填，签名
        jsApiList: ["chooseWXPay"], // 必填，需要使用的JS接口列表
      });
    },
    getEventList() {
      activeApi.GetEventList().then(res => {
        this.eventList = res.map(i => ({
          ...i,
          name: i.name ? i.name.split(",") : []
        }));
      })
    },
    send_sms() {
      if (this.isPhoneExp(this.formModel.mobile) && this.isMobile) {
        if (!["获取验证码", "重新获取"].includes(this.button_text)) {
          uni.showToast({
            title: "请再倒计时结束后再重新发送验证码！",
            duration: 2000,
            icon: "none",
          });
          return false;
        }
        activeApi.GetSendSMS({ mobile: this.formModel.mobile }).then((res) => {
          // console.log(res);
          if (res.statusCode == 200) {
            uni.showToast({
              title: "发送验证码成功",
              duration: 2000,
              icon: "none",
            });
            this.time = 60;
            this.time_down();
            this.code_blur = false;
          }
        });
      } else {
        uni.showToast({
          title: "请输入正确的手机号",
          duration: 2000,
          icon: "none",
        });
      }
    },
    handleMobileBlur() {
      if (this.formModel.mobile) {
        if (this.isPhoneExp(this.formModel.mobile)) {
          this.isMobile = true;
        } else {
          this.isMobile = false;
        }
        activeApi.GetEventLevel({ student_mobile: this.formModel.mobile }).then(res => {
          if (res && res.length) {
            this.resLevels = res;
            this.isDisabledLevel = true;
          } else {
            this.levelList = this.standbyLevels;
            this.isDisabledLevel = false;
            this.resLevels = "";
            this.index = "";
          }
          this.handleNameBlur();
        })
      }
    },
    handleNameBlur() {
      console.log(this.resLevels, "this.resLevels");
      if (this.resLevels) {
        this.formModel.level = this.resLevels;
        this.index = 0;
        this.isDisabledLevel = true;
        this.levelList = [{ level: this.resLevels }];
      } else {
          this.levelList = this.standbyLevels;
          this.isDisabledLevel = false;
          this.index = "";
      }
      console.log(this.resLevels, this.formModel.name, this.levelList, this.isDisabledLevel);
    },
    time_down() {
      if (this.time > 0) {
        this.time--;
        this.button_text = this.time + "s后重发";
        setTimeout(this.time_down, 1000);
      } else {
        this.time = 0;
        this.button_text = "重新获取";
        this.code_blur = true;
      }
    },
    bindPickerChange({ detail }) {
      this.formModel.level = this.levelList[detail.value].level;
      this.index = detail.value;
    },
    validate() {
      if (this.formModel.event_ids.length === 0) {
        return {
          isError: true,
          message: "请选择参赛活动"
        };
      } else if (!this.isPhoneExp(this.formModel.mobile)) {
        return {
          isError: true,
          message: "请输入正确的手机号"
        };
      } else if (!this.formModel.name) {
        return {
          isError: true,
          message: "请输入姓名"
        };
      } else if (!this.formModel.sms_code) {
        return {
          isError: false,
          message: "请输入验证码"
        };
      } else if (!this.formModel.address) {
        return {
          isError: true,
          message: "请输入邮寄地址"
        };
      } else if (!this.formModel.class_name) {
        return {
          isError: true,
          message: "请输入在读年级"
        };
      } else {
        return {
          isError: false,
          message: ""
        };
      }
    },
    handleEventClick(item) {
      if (!this.formModel.event_ids.includes(item.id)) {
        this.formModel.event_ids.push(item.id);
        this.totalPrice += item.price;
      } else {
        this.formModel.event_ids.splice(this.formModel.event_ids.indexOf(item.id), 1)
        this.totalPrice -= item.price;
      }
    },
    save(pay_type) {
      if (this.validate().isError) {
        uni.showToast({
          title: this.validate().message,
          duration: 2000,
          icon: "none",
        });
        return;
      }
      this.payBtnLoading = true;
      this.h5_wechat_pay(pay_type);
    },
    h5_wechat_pay(pay_type) {
      const sign_type = this.formModel.sign_type;
      const isSignPage = sign_type === "sign";
      var _self = this;
      console.log(pay_type, "h5_wechat_pay")
      console.log(this.formModel, "h5_wechat_pay")
      console.log(sign_type, isSignPage, "h5_wechat_pay")
      uni.hideLoading();
      if (pay_type === "wechat") {
        this.formModel.trade_type = this.isWXBrowser() ? "JSAPI" : "MWEB";
      }

      activeApi.CreateOrder(this.formModel).then((res) => {
        console.log(res, "CreateOrder")
        if (res.statusCode == 200) {
          this.payChannelModal = false;
          this.payBtnLoading = false;
          if (isSignPage) {
            uni.navigateTo({
              url: `/pages/activePage/statusPage/success?text=报名成功&qrcodeSrc=${res.data.qr_code}`,
            });
          } else {
            if (pay_type === "wechat") {
              if (this.isWXBrowser()) {
                wx.chooseWXPay({
                  appId: res.data.appId,
                  timestamp: res.data.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
                  nonceStr: res.data.nonceStr, // 支付签名随机串，不长于 32 位
                  package: res.data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
                  signType: res.data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                  paySign: res.data.paySign, // 支付签名
                  success: function () {
                    /* eslint-disable */
                    // uni.showLoading({
                    //   title: "加载中",
                    // });
                    uni.navigateTo({
                      url: `/pages/activePage/statusPage/success?text=支付成功&qrcodeSrc=${res.data.qr_code}`,
                    });
                  },
                  cancel: function (res) {
                    uni.hideLoading();
                    uni.showToast({
                      title: "支付失败",
                      duration: 2000,
                      icon: "none",
                    });
                    uni.navigateTo({
                      url: `/pages/activePage/statusPage/error?text=支付失败&qrcodeSrc=${res.data.qr_code}`,
                    });
                    /* eslint-disable */
                    // _self.$refs.confirm_dialog.open();
                    /* eslint-enable */
                  },
                  fail: function (res) {
                    /* eslint-disable */
                    // console.log(res);
                    /* eslint-enable */
                  },
                });
              } else {
                window.location.replace(res.data.mweb_url + "&redirect_url=" + encodeURIComponent(window.location.href + "&from=wechat&order_number=" + res.data.order_number));
              }
            } else {
              window.location.replace(res.data.pay_url);
            }
            _self.payListener(
              res.data.order_number
            );
          }
        } else {
          uni.navigateTo({
            url: `/pages/activePage/statusPage/error?text=${isSignPage ? "报名" : "支付"}失败&sign_type=${isSignPage ? "sign" : ""}&qrcodeSrc=${res.data.qr_code}`,
          });
        }
      })
      .catch(() => {
        // uni.navigateTo({
        //   url: `/pages/activePage/statusPage/error?text=${isSignPage ? "报名" : "支付"}失败&sign_type=${isSignPage ? "sign" : ""}`,
        // });
        this.payBtnLoading = false;
        this.pay_channel = "";
      });
    },
    payMoney() {
      if (this.validate().isError) {
        uni.showToast({
          title: this.validate().message,
          duration: 2000,
          icon: "none",
        });
        return;
      }
      if (this.isWXBrowser()) {
        this.formModel.pay_method = "wechat";
        this.save("wechat");
      } else {
        this.payChannelModal = true;
      }
    },
    handleChannelClose() {
      this.payChannelModal = false;
      this.pay_channel = "";
    },
    radioChange({ detail }) {
      this.pay_channel = detail.value;
      // this.formModel.ali_callback = this.pay_channel === "aliPay" ? window.location.href + "&from=aliPay&order_number=" : "";
      this.formModel.pay_method = detail.value;
      // this.save(detail.value);
    },
    isWXBrowser() {
      let ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          return true; // 微信中打开
      } else {
          return false; // 普通浏览器中打开
      }
    },
    payListener(order_number) {
      // this.payBtnLoading = false;
      // this.pay_color = "#FF6644";
      if (this.payListenerTimer) {
        clearInterval(this.payListenerTimer);
      }
      this.payListenerTimer = setInterval(() => {
        activeApi.GetPayResult({ order_number }).then((res) => {
          console.log(res, "GetPayResult")
          if (res.data.pay_status != "in_pay") {
            clearInterval(this.payListenerTimer);
          } else {
            uni.hideLoading();
          }
          if (res.data.pay_status == "no_pay") {
            uni.showToast({
              title: "支付失败",
              duration: 2000,
              icon: "none",
            });
            uni.navigateTo({
              url: `/pages/activePage/statusPage/error?text=支付失败&qrcodeSrc=${res.data.qr_code}`,
            });
          }
          if (res.data.pay_status == "is_pay") {
            uni.showToast({
              title: "支付成功",
              duration: 2000,
              icon: "success",
            });
            uni.navigateTo({
              url: `/pages/activePage/statusPage/success?text=支付成功&qrcodeSrc=${res.data.qr_code}`,
            });
          }
        });
      }, 1000);
    }
  },
  watch: {},
  // 页面周期函数--监听页面加载
  onLoad(option) {
    // new vconsole();
    this.formModel.sign_type = option.sign_type;
    this.options = option;
    console.log(this.isWXBrowser());
    if (this.isWXBrowser()) {
      this.wechat = true;
      if (!option.code) {
        this.wechatDirectUrl();
      } else {
        this.wechatPay();
      }
    }
    console.log(option, "---->option");
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.getEventList();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
} 
</script>

<style lang="scss" scoped>
.active1 {
  .bg {
    width: 100%;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .form {
    padding: 120rpx 100rpx 100px 80rpx;
    background: url(../../../public/active/bg3.jpg) no-repeat;
    background-size: 100% 100%;
  }
  .uni-form-item {
    display: flex;
    margin: 15rpx 0;
    // .triangle-down {
    //   width: 0;
    //   height: 0;
    //   border-left: 10rpx solid transparent;
    //   border-right: 10rpx solid transparent;
    //   border-top: 20rpx solid #ddd;
    // }
    .title {
      font-size: 28rpx;
      font-weight: 500;
      width: 165rpx;
      padding-right: 8rpx;
      .required {
       color: #f56c6c; 
      }
    }
    .event-list {
      margin-top: 15rpx;
      padding-bottom: 44rpx;
      .event-item {
        &.checked {
          border: 1px solid #fc596a;
          color: #fc596a;
          .checked-icon {
            width: 15px;
            height: 15px;
            border-top-right-radius: 5px;
            border-bottom-left-radius: 11px;
            position: absolute;
            right: 0;
            top: 0;
            background: #fc596a;
          }
        }
        padding: 20rpx 35rpx;
        margin-bottom: 15rpx;
        display: inline-block;
        background: #fff;
        vertical-align: middle;
        margin-right: 20rpx;
        border: 1px solid #ffa8b1;
        position: relative;
        color: #9b9b9b;
        font-size: 26rpx;
        font-weight: 500;
        border-radius: 20rpx;
      }
    }
  }
  .checkbox {
    margin-right: 20rpx;
    &:last-child {
      margin-right: 0;
    }
    .checkbox-item {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .code_btn {
    width: 160rpx;
    height: 48rpx;
    border-radius: 24rpx;
    background: #cacdd2;
    font-size: 24rpx;
    text-align: center;
    line-height: 48rpx;
    color: #fff;
  }
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 19rpx 0;
    height: 120rpx;
    background: #fff;
    .footer-mian {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .price, .unit {
        color: #eb524b;
      }
      .unit {
        font-size: 28rpx;
        font-weight: 500;
      }
      .price {
        font-size: 50rpx;
        font-weight: 600;
      }
    }
  }
  .pay-channel-modal {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: #00000069;
    top: 0;
    position: fixed;
    .pay-channel-modal-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      .pay_container {
        width: 640rpx;
        // height: 300rpx;
        border-radius: 15rpx;
        padding: 30rpx 0 15rpx 0;
        background: #fff;
        position: relative;
        .close {
          position: absolute;
          right: 32rpx;
          top: 11rpx;
          font-size: 60rpx;
          line-height: 1;
          color: #666;
        }
        .order_title {
          display: flex;
          height: 28rpx;
          align-items: center;
          margin-bottom: 48rpx;
          padding-bottom: 34rpx;
          border-bottom: 1px solid #ddd;
          padding-left: 40rpx;
          .title_line {
            width: 6rpx;
            height: 28rpx;
            background: #ff6644;
            border-radius: 4rpx;
            margin-right: 12rpx;
          }
        }
        .pay-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          // width: 100%;
          height: 80rpx;
          background: #fff;
          margin: 0 40rpx 32rpx 40rpx;
          padding-bottom: 32rpx;
          border-bottom: 1px solid #ddd;
          .pay-label-container {
            display: flex;
            align-items: center;
            height: 100%;
            justify-content: flex-end;
          }
          .pay-logo {
            width: 80rpx;
            height: 80rpx;
            margin-right: 16rpx;
          }
          .pay-label-box {
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: space-between;
            align-items: flex-start;
          }
          .pay-label {
            font-size: 32rpx;
            line-height: 40rpx;
            font-weight: bold;
            color: #333333;
          }
          .pay-issue {
            color: #abadb0;
            font-size: 24rpx;
            line-height: 32rpx;
          }
        }
      }
    }
  }
}
</style>