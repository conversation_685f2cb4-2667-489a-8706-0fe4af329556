{
    "name" : "聂卫平围棋网校",
    "appid" : "__UNI__1FF3C9C",
    "description" : "",
    "versionName" : "1.4.5",
    "versionCode" : 79,
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "screenOrientation" : [ "portrait-primary" ],
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "VideoPlayer" : {},
            "Payment" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            "ios" : {
                "idfa" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "oauth" : {},
                "share" : {},
                "payment" : {
                    "appleiap" : {}
                },
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "android" : {
                    "hdpi" : "public/kaipingPage/480_762.png",
                    "xhdpi" : "public/kaipingPage/720_1242.9.png",
                    "xxhdpi" : "public/kaipingPage/1080_1882.9.png"
                },
                "ios" : {
                    "iphone" : {
                        "portrait-896h@3x" : "public/kaipingPage/1242_2688.png",
                        "portrait-896h@2x" : "public/kaipingPage/828_1792.png",
                        "iphonex" : "public/kaipingPage/1125_2436.png",
                        "retina55" : "public/kaipingPage/1242_2208.png",
                        "retina47" : "public/kaipingPage/750_1334.png",
                        "retina40" : "public/kaipingPage/640_1136.png",
                        "retina35" : "public/kaipingPage/640_960.png"
                    }
                }
            }
        },
        "compilerVersion" : 2,
        "uniStatistics" : {
            "enable" : true
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 微信小程序特有相关 */
        "appid" : "",
        "setting" : {
            "urlCheck" : false,
            "postcss" : false
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "devServer" : {
            "disableHostCheck" : true,
            "https" : false
        },
        "router" : {
            "mode" : "history",
            "base" : "/"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "title" : "聂卫平围棋网校",
        "uniStatistics" : {
            "enable" : true
        }
    },
    "uniStatistics" : {
        "enable" : true,
        "version" : "1"
    }
}
/* 应用发布信息 *//* android打包配置 *//* ios打包配置 */

