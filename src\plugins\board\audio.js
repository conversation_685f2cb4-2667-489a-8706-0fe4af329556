const sec30url =
    "https://cdn.elf-go.com/7b03e5a3a7734c24b290bc0354383f07/f20321b9ec494e9a9f83c1e338040fd9-9678a3d35e94c95f8f7ea38c556b2816-sq.mp3";
const sec20url =
    "https://cdn.elf-go.com/ee7a91f9f6a847fc9e6389d816a6fd90/df55e85126d84a3c9448600deebe2c56-330ccf4b232c0a75bad74f62d663abf5-sq.mp3";
const sec10url =
    "https://cdn.elf-go.com/8e6a8334f98b43a8838b078c48258997/ebeb860ebda24e178227c0e69259e9ef-9f600363f099597f118aa0c1ec3183c8-sq.mp3";
const sec9url =
    "https://cdn.elf-go.com/f100a3e4246c4e0198e691293c44559c/6458e9e236a44020b526d209f7f48510-00a41357dd7b758893cf80aaadb488e1-sq.mp3";
const sec8url =
    "https://cdn.elf-go.com/3f80be718c7c451db938596938154fe7/51ebcd65378d43dc904c988b8e99bf84-bc00aebd31771c7ddc3b4893f833d33d-sq.mp3";
const sec7url =
    "https://cdn.elf-go.com/f955b99b2dd64d079c90a69c22fea4bd/39527eef93194258aefacf4fab0d0e35-51adea3c89b1a7921408d730c7013367-sq.mp3";
const sec6url =
    "https://cdn.elf-go.com/bc2f86b5da4f44278a89ca31e9425641/27ad05b788354828ba0d1b7eba148476-29128d86b747e8f218b77f1e30052a3b-sq.mp3";
const sec5url =
    "https://cdn.elf-go.com/6c34f54fc9fd46528cfec2d60af2c230/496838d6fab14e63a2613773ec07e367-2d76a70215080acefd2294cabe66b786-sq.mp3";
const sec4url =
    "https://cdn.elf-go.com/b0756c512e634614871f541368693574/41ac6bd79cfb44f6a1293e5ae458098c-51c0be0f11be3430a51e2d7999f4efe0-sq.mp3";
const sec3url =
    "https://cdn.elf-go.com/bdd21bd9a9a2422989537f25b428c00e/2155b8fbd55e449bbfe9c33ebd1288f1-2ab5c099064cc2c2f09cd479da3667b1-sq.mp3";
const sec2url =
    "https://cdn.elf-go.com/49d513d3034f435c8a7fde6870d0cce4/1861302be4944595b2e70d122f41b482-dadd3ba6350f76469e5af47b77255a36-sq.mp3";
const sec1url =
    "https://cdn.elf-go.com/8c30b44db43f42feb35767d84757ef52/33225de9095345bca80987fb48596be0-b3fa2b40612d8636e6ff43d73a8f8468-sq.mp3";
const secStartUrl =
    "https://cdn.elf-go.com/f74efbec43554a2fbb40fcf4cd143d1e/716b5615dd2c444499d675282a86f2cd-0beaa52f7e3abbdbc9f79b01d8b80ef2-sq.mp3";
const secOverUrl =
    "https://cdn.elf-go.com/53af7e0cdfe943bf9bd902d34bb08f19/5dd291d62d234bfc91f6e521dd5fbe4f-56aff1ca39c004979c6810f3c7de49de-sq.mp3";
var innerAudioContext1 = uni.createInnerAudioContext();
var innerAudioContext2 = uni.createInnerAudioContext();
var innerAudioContext3 = uni.createInnerAudioContext();
function playVoice(paly_dom, voice_url) {
    setTimeout(() => {
        paly_dom.src = voice_url;
        paly_dom.play();
    }, 0);
}
function playeLeftTimeAudio(numb) {
    switch (numb) {
        case 30:
            playVoice(innerAudioContext1, sec30url);
            break;
        case 20:
            playVoice(innerAudioContext1, sec20url);
            break;
        case 10:
            playVoice(innerAudioContext1, sec10url);
            break;
        case 9:
            playVoice(innerAudioContext2, sec9url);
            break;
        case 8:
            playVoice(innerAudioContext3, sec8url);
            break;
        case 7:
            playVoice(innerAudioContext3, sec7url);
            break;
        case 6:
            playVoice(innerAudioContext3, sec6url);
            break;
        case 5:
            playVoice(innerAudioContext1, sec5url);
            break;
        case 4:
            playVoice(innerAudioContext1, sec4url);
            break;
        case 3:
            playVoice(innerAudioContext2, sec3url);
            break;
        case 2:
            playVoice(innerAudioContext3, sec2url);
            break;
        case 1:
            playVoice(innerAudioContext3, sec1url);
            break;
        case "start":
            playVoice(innerAudioContext3, secStartUrl);
            break;
        case "over":
            playVoice(innerAudioContext3, secOverUrl);
            break;
    }
}
export default {
    PlayeLeftTimeAudio(numb) {
        return playeLeftTimeAudio(numb);
    }
};
