import config from "./config.js";
let BaseUrl = config.baseUrl;

function request(params, method, data) {
  let token = uni.getStorageSync("user_token") || "";
  let header = {};
  let URL = "";
  if (params.includes("http://") || params.includes("https://")) {
    URL = params;
  } else {
    URL = BaseUrl + params;
  }
  if (token) {
    header = {
      "content-type": "application/json",
      token: `${token}`,
      ...params.header
    };
  } else {
    header = {
      "content-type": "application/json",
      ...params.header
    };
  }
  return new Promise(function (resolve, reject) {
    uni.request({
      url: URL,
      method: method,
      data: data,
      header: header,
      timeout: 10000,
      success(res) {
        // 成功回调
        if (res.statusCode === 401) {
          // cookies.DeleteCookies();
          // 只有在当前路由不是登录页面才跳转
          // uni.redirectTo({
          //   url: "/pages/login/index"
          // });
        } else if (res.statusCode === 400) {
          let isShowToast = data.isShowToast == false ? false : true;
          if (
            res.data.hasOwnProperty("error_code") &&
            res.data.error_code !== "record not found"
          ) {
            if (isShowToast) {
              uni.showToast({
                title: res.data.error_code,
                duration: 2000,
                icon: "none"
              });
            }
            reject(res);
          }
        } else {
          resolve(res);
        }
      },
      fail(err) {
        uni.hideLoading();
        console.log(err)
        if (err.errMsg === "request:fail timeout") {
          uni.showToast({
            title: "请检查您的网络连接是否正常",
            duration: 2000,
            icon: "none"
          });
        }
        reject(err);
        // 失败回调
      },
      complete() {
        //uni.hideLoading()
        // 无论成功或失败 只要请求完成的 回调
      }
    });
  });
}
export default {
  get(params, data) {
    return request(params, "GET", data);
  },
  post(params, data) {
    return request(params, "POST", data);
  },
  put(params, data) {
    return request(params, "PUT", data);
  }
};
