<template>
  <view class="flex-col space-y-43 page">
    <view class="flex-col space-y-21">
      <view class="flex-col group_4">
        <image
          class="image_5"
          src="../../static/login_bg.png"
        />
        <view class="flex-col section">
          <text class="text_3">验证码登录</text>
          <view class="flex-row space-x-29 group_5">
            <image
              class="image_6"
              src="../../static/mobile.png"
            />
            <input
              class="uniInput"
              type="number"
              maxlength="11"
              placeholder="请输入手机号"
              v-model="mobile"
            />
          </view>
          <view class="flex-row justify-between group_6">
            <view class="flex-row space-x-24 group_7">
              <image
                class="image_7"
                src="../../static/code.png"
              />
              <input
                class="uniInput"
                type="number"
                maxlength="6"
                placeholder="请输入验证码"
                v-model="sms_code"
              />
            </view>
            <view class="flex-col items-center text-wrapper-2" v-if="sms_focus" @click="send_sms">
              <text class="font_1 text_4">{{button_text}}</text>
            </view>
            <view class="flex-col items-center text-wrapper" v-else>
              <text class="font_1 text_4">{{button_text}}</text>
            </view>
          </view>
          <view class="flex-col items-center text-wrapper_3" v-if="btn_focus" @click="login">
            <text class="text_5">登录</text>
          </view>
          <view class="flex-col items-center text-wrapper_2" v-else>
            <text class="text_5">登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import userApi from "../../api/user";
import matchApi from "../../api/elaluation";
export default {
  components: {},
  data() {
    return {
      sms_focus: false,
      btn_focus: false,
      mobile: "",
      sms_code: "",
      button_text: "获取验证码",
      time: 60
    };
  },
  watch: {
    mobile(new_str) {
      if (this.isPhoneExp(new_str)) {
        this.sms_focus = true;
      } else {
        this.sms_focus = false;
      }
    },
    sms_code(new_num) {
      if (new_num && this.isPhoneExp(this.mobile)) {
        this.btn_focus = true;
      } else {
        this.btn_focus = false;
      }
    }
  },
  methods: {
    isPhoneExp(str) {
      let pattern = /^1(3|4|5|6|7|8|9)\d{9}$/;
      return pattern.test(str);
    },
    send_sms() {
      if (
        (this.button_text === "获取验证码" ||
          this.button_text === "重新获取") &&
        this.mobile
      ) {
        if (this.isPhoneExp(this.mobile)) {
          userApi.GetSendSMS({ mobile: this.mobile }).then(res => {
            // console.log(res);
            if (res.statusCode == 200) {
              this.time = 60;
              this.time_down();
              this.sms_focus = false;
            }
          });
        } else {
          uni.showToast({
            title: "请输入正确的手机号",
            duration: 1000,
            icon: "none"
          });
        }
      }
    },
    time_down() {
      if (this.time > 0) {
        this.time--;
        this.button_text = this.time + "s后重发";
        setTimeout(this.time_down, 1000);
      } else {
        this.time = 0;
        this.button_text = "重新获取";
        this.code_blur = true;
      }
    },
    login() {
      let obj = {
        mobile: this.mobile,
        sms_code: this.sms_code
      };
      matchApi.Login(obj).then(res => {
        console.log(res);
        uni.setStorageSync("user_token",res.data.token);
        uni.navigateTo({
          url: `/pages/evaluation/evaluation`
        });
      });
    }
  }
};
</script>
<style scoped>
.page {
  background-color: #ffffff;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}
.space-y-43 > view:not(:first-child),
.space-y-43 > text:not(:first-child),
.space-y-43 > image:not(:first-child) {
  margin-top: 43rpx;
}
.space-y-21 > view:not(:first-child),
.space-y-21 > text:not(:first-child),
.space-y-21 > image:not(:first-child) {
  margin-top: 21rpx;
}
.group_4 {
  height: 1448rpx;
}
.image_5 {
  flex-shrink: 0;
  width: 750rpx;
  height: 420rpx;
}
.section {
  margin-top: -185rpx;
  padding: 52rpx 50rpx 612rpx;
  background-color: #ffffff;
  border-radius: 30rpx 30rpx 0px 0px;
  position: relative;
}
.text_3 {
  align-self: flex-start;
  color: #333333;
  font-size: 40rpx;
  font-family: "PingFang SC";
  font-weight: 600;
  line-height: 56rpx;
}
.group_5 {
  margin-top: 49rpx;
  padding: 0 6rpx;
  display: flex;
}
.uniInput {
  margin-left: 24rpx;
  width: 300rpx;
}
.space-x-29 > view:not(:first-child),
.space-x-29 > text:not(:first-child),
.space-x-29 > image:not(:first-child) {
  margin-left: 29rpx;
}
.image_6 {
  margin: 3rpx 0;
  width: 36rpx;
  height: 36rpx;
}
.font_1 {
  font-size: 30rpx;
  font-family: "PingFang SC";
  line-height: 42rpx;
  color: #999999;
}
.group_6 {
  margin-top: 37rpx;
  padding: 38rpx 0 20rpx;
  border-top: solid 2rpx #f1f1f1;
  border-bottom: solid 2rpx #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.group_7 {
  align-self: center;
  display: flex;
}
.space-x-24 > view:not(:first-child),
.space-x-24 > text:not(:first-child),
.space-x-24 > image:not(:first-child) {
  margin-left: 24rpx;
}
.image_7 {
  margin: 8rpx 0 7rpx;
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
}
.text-wrapper {
  background-color: #cacdd2;
  border-radius: 40rpx;
  width: 218rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
}
.text-wrapper-2 {
  background-color: #31bfff;
  border-radius: 40rpx;
  width: 218rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
}

.text_4 {
  color: #ffffff;
}
.text-wrapper_2 {
  margin-top: 112rpx;
  line-height: 96rpx;
  background-color: #cacdd2;
  border-radius: 48rpx;
  text-align: center;
  line-height: 96rpx;
}
.text-wrapper_3 {
  margin-top: 112rpx;
  line-height: 96rpx;
  background-color: #31bfff;
  border-radius: 48rpx;
  text-align: center;
  line-height: 96rpx;
}
.text_5 {
  color: #ffffff;
  font-size: 34rpx;
  font-family: "PingFang SC";
  font-weight: 500;
  line-height: 48rpx;
}
</style>