<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0,viewport-fit=cover" />
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      document.documentElement.style.fontSize =
        document.documentElement.clientWidth / 20 + "px"
    })
    var coverSupport =
      "CSS" in window &&
      typeof CSS.supports === "function" &&
      (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ", viewport-fit=cover" : "") +
      '" />'
    )


  </script>
  <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
  <script>!function (g, d, t, e, v, n, s) { if (g.gdt) return; v = g.gdt = function () { v.tk ? v.tk.apply(v, arguments) : v.queue.push(arguments) }; v.sv = '1.0'; v.bt = 0; v.queue = []; n = d.createElement(t); n.async = !0; n.src = e; s = d.getElementsByTagName(t)[0]; s.parentNode.insertBefore(n, s); }(window, document, 'script', '//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js'); gdt('init', '1201077621'); gdt('track', 'PAGE_VIEW');</script>
  <noscript><img height="1" width="1" style="display:none"
      src="https://a.gdt.qq.com/pixel?user_action_set_id=1201077621&action_type=PAGE_VIEW&noscript=1" /></noscript>
</head>

<body>
  <noscript>
    <strong>Please enable JavaScript to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>