// 项目公共配置
let config = {
  // #ifdef H5
  baseUrl:
    `${process.env.NODE_ENV}` === "development" //开发环境
      // ? "http://**********:8080"
        ?"https://h5-tw-dev.elf-go.com"
      : `${document.location.protocol}//${document.location.host}`,
  baseWebSocket:
    `${process.env.NODE_ENV}` === "development" //生产环境
      // ? "ws://**********:8080"
        ?"wss://h5-tw.elf-go.com"
      : `${document.location.protocol === "https:" ? "wss:" : "ws:"}//${
          document.location.host
        }`,
  // #endif
  // #ifdef APP-PLUS 
  baseUrl:
    `${process.env.NODE_ENV}` === "development" //开发环境
      ? "https://h5-tw.elf-go.com"
      : "https://h5-tw.elf-go.com", //"https://higo-staging.elf-go.com",
  baseWebSocket:
    `${process.env.NODE_ENV}` === "development" //生产环境
      ? "wss://h5-tw.elf-go.com"
      : "wss://h5-tw.elf-go.com",
  // #endif
  // 图片前缀
  imgPreUrl: "",
  // 无图时默认图片
  imgDefault: "",
  // 在微信浏览器下 是否获取code
  get_wx_code: false,
  /* 授权方式 静默授权 手动授权
         snsapi_base 不弹出授权页面，直接跳转，只能获取用户openid
         snsapi_userinfo 弹出授权页面，可通过openid拿到昵称、性别、所在地。并且， 即使在未关注的情况下，只要用户授权，也能获取其信息
     */
  wx_code_type: "snsapi_userinfo"
};
export default {
  install: function(Vue) {
    Vue.prototype.$config = config;
  },
  ...config
};
