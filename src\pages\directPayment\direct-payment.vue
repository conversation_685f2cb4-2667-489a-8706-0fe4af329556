<template>
  <view class="content">
    <view class="img-container">
      <image
        class="logo"
        mode="widthFix"
        :src="
          info.price !== 0
            ? '../../static/index-image.jpg'
            : '../../static/zero_index_image.jpg'
        "
      />
      <view class="blank-container"></view>
    </view>
    <!-- <view class="blank-container"></view> -->
    <view class="bottom_shop">
      <view class="input-container">
        <view class="uni_input mobile-input-container">
          <input
            placeholder="手机号"
            placeholder-style="color: #CECECE;"
            v-model="mobile"
            class="mobile-inut"
          />
        </view>
        <view class="uni_input">
          <input
            class="code_input"
            placeholder="验证码"
            placeholder-style="color: #CECECE;"
            v-model="sms_code"
          />
          <view class="code_btn" v-if="code_blur" @click="send_sms">{{
            button_text
          }}</view>

          <view
            class="code_btn"
            :class="{ 'disable-button': isDisabled }"
            v-else
            @click="send_sms"
            >{{ button_text }}</view
          >
        </view></view
      >
      <view class="shop_btn goPay-button" @click="goOrder">
        立即支付 ¥{{ info.price ? (info.price / 100).toFixed(2) : 0 }}</view
      >
    </view>
    <uni-popup ref="popup" type="bottom" :mask-click="false">
      <view class="select-payment-popop">
        <view class="header-title">
          <view>确认支付</view>
          <image
            class="close-icon"
            mode="widthFix"
            src="../../static/close-popup.png"
            @click="closePopop"
          />
        </view>

        <view class="pay_container">
          <radio-group @change="radioChange">
            <label v-for="(item, index) in payList" :key="item.value">
              <view
                class="pay-item"
                v-if="wechat ? item.value !== 'aliPay' : true"
              >
                <view class="pay-label-container">
                  <img class="pay-logo" :src="item.imgUrl" />
                  <view class="pay-label-box">
                    <view class="pay-label">{{ item.label }}</view>
                    <view class="pay-issue">银行卡/信用卡都可以使用</view></view
                  >
                </view>

                <view>
                  <radio
                    :value="item.value"
                    :checked="index === current"
                    color="#FF5655"
                    class="radio"
                  /> </view
              ></view>
              <view class="line" v-if="index !== payList.length - 1"></view>
            </label>
          </radio-group>
        </view>
        <view class="shop_btn confirm-pay-button" @click="reallyPay">
          立即支付 ¥{{ info.price ? (info.price / 100).toFixed(2) : 0 }}
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
!(function(g, d, t, e, v, n, s) {
  if (g.gdt) return;
  v = g.gdt = function() {
    v.tk ? v.tk.apply(v, arguments) : v.queue.push(arguments);
  };
  v.sv = "1.0";
  v.bt = 0;
  v.queue = [];
  n = d.createElement(t);
  n.async = !0;
  n.src = e;
  s = d.getElementsByTagName(t)[0];
  s.parentNode.insertBefore(n, s);
})(
  window,
  document,
  "script",
  "//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js"
);
gdt("init", "1201077621");

import userApi from "../../api/user";
import wx from "jweixin-module";

export default {
  data() {
    return {
      title: "Hello",
      info: {},
      options: {},
      mobile: "",
      button_text: "获取验证码",
      wechat: false,
      sms_code: "",
      select: false,
      mobile: "",
      code_blur: false,
      button_text: "获取验证码",
      name: "",
      time: 60,
      orderInfo: {},
      pay_color: "#FF6644",
      code: "",
      open_id: "",
      isDisabled: false,
      payList: [
        {
          label: "微信支付",
          value: "wechat",
          imgUrl: "../../static/wechatPay.png",
        },
        {
          label: "支付宝支付",
          value: "aliPay",
          imgUrl: "../../static/aliPay.png",
        },
      ],
      current: 0,
      pay_channel: "",
      payListenerTimer: null,
    };
  },
  watch: {
    mobile(new_str) {
      if (this.isPhoneExp(new_str)) {
        this.code_blur = true;
      } else {
        this.code_blur = false;
      }
    },
  },
  onLoad(option) {
    if (/micromessenger/.test(navigator.userAgent.toLowerCase())) {
      this.wechat = true;
    }
    uni.showLoading({
      title: "加载中",
    });

    this.options = option;
    gdt("track", "PAGE_VIEW", { prouct_id: option.id });
    this.initInfo(option.id);
    if (option.from == "wechat" || option.from == "aliPay") {
      uni.showLoading({
        title: "订单支付中..",
      });
      this.payListener(option.order_number);
    }
  },
  destroyed() {
    uni.hideLoading();
  },
  methods: {
    initInfo(id) {
      uni.showLoading({
        title: "加载中",
      });

      userApi.GetCourseInfo({ merchandise_id: id }).then((res) => {
        uni.hideLoading();
        this.info = res.data;
        if (res.data.merchandise_name) {
          uni.setStorageSync("merchandise_name", res.data.merchandise_name);
        }
        this.open_id = uni.getStorageSync("open_id") || "";
        //微信浏览器且openid存在且金额大于0
        if (
          /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
          this.open_id &&
          this.info.price > 0
        ) {
          this.wechatH5Sign();
        } else {
          if (
            /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
            this.info.price > 0 &&
            !this.options.code
          ) {
            this.wechatDirectUrl();
          } else if (
            /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
            this.info.price > 0 &&
            this.options.code
          ) {
            this.wechatPay();
          }
        }
      });
    },
    wechatDirectUrl() {
      userApi.weChatH5Auth({ url: window.location.href }).then((res) => {
        uni.hideLoading();
        if (res.data) {
          window.location.replace(res.data.data.redirect_url);
        }
      });
    },
    async wechatPay() {
      let res = await userApi.weChatH5Auth({
        code: this.options.code,
        url: window.location.href,
      });
      this.open_id = res.data.data.open_id;
      uni.setStorageSync("open_id", res.data.data.open_id);
      if (!this.open_id) return;
      this.wechatH5Sign();
    },
    async wechatH5Sign() {
      let res = await userApi.weChatH5Sign({ url: window.location.href });
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: res.data.data.appId, // 必填，公众号的唯一标识
        timestamp: res.data.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: res.data.data.noncestr, // 必填，生成签名的随机串
        signature: res.data.data.signature, // 必填，签名
        jsApiList: ["chooseWXPay"], // 必填，需要使用的JS接口列表
      });
    },
    get_adv_source() {
      // 抖音h5链接参数
      // clickid	--> d_click_id
      // account_id
      // ad_id
      // creative_id

      // 腾讯h5链接参数
      // clikd_id --> t_click_id
      // account_id
      // ad_id
      // adcreative_id --> creative_id
      const tx_source = uni.getStorageSync("tx_source");
      const dy_source = uni.getStorageSync("dy_source");
      const click_url = uni.getStorageSync("click_url");
      let obj = {
        d_click_id: "",
        t_click_id: "",
        account_id: "",
        ad_id: "",
        creative_id: "",
        click_url,
      };
      if (tx_source) {
        obj = {
          t_click_id: tx_source.click_id || "",
          account_id: tx_source.account_id || "",
          ad_id: tx_source.ad_id || "",
          creative_id: tx_source.adcreative_id || "",
          click_url,
        };
      }
      if (dy_source) {
        obj = {
          d_click_id: dy_source.clickid || "",
          account_id: dy_source.accountid || "",
          ad_id: dy_source.adid || "",
          creative_id: dy_source.creativeid || "",
          project_id: dy_source.projectid || "",
          click_url,
        };
      }
      return obj;
    },
    h5_wechat_pay() {
      if (/micromessenger/.test(navigator.userAgent.toLowerCase())) {
        var _self = this;
        let _uni = uni;
        let url = window.location.href;
        const source_params = this.get_adv_source();
        let trackId = uni.getStorageSync("trackId") || "";
        uni.hideLoading();
        let from = {
          mobile: this.mobile,
          sms_code: this.sms_code,
          source: this.options.source ? this.options.source : "",
          course_id: this.info.id,
          open_id: this.open_id,
          trade_type: "JSAPI",
          tw_callback: uni.getStorageSync("tw_callback"),
          click_id: uni.getStorageSync("clickid"),
          teacher_id: this.options?.teacher_id || "",
          po_id: this.options.po_id ? this.options.po_id : "",
          ...source_params,
          trackId,
          recommend_id: this.options.recommend_id || "",
          is_train: this.options.is_train || "",
        };

        userApi.CreateOrder(from).then((res) => {
          wx.chooseWXPay({
            appId: res.data.appId,
            timestamp: res.data.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
            nonceStr: res.data.nonceStr, // 支付签名随机串，不长于 32 位
            package: res.data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
            signType: res.data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
            paySign: res.data.paySign, // 支付签名
            success: function() {
              /* eslint-disable */
              uni.showLoading({
                title: "加载中",
              });
              let clickid = uni.getStorageSync("clickid");

              if (clickid) {
                uni.showLoading({
                  title: clickid,
                });
                _self.initClick(clickid, res.data.qr_code);
              } else {
                // let bdVid = uni.getStorageSync("bdVid");
                // if (bdVid) {
                //   let url = window.location.href;
                //   userApi
                //     .toBaidu({
                //       logidUrl: `${url}&bd_vid=${bdVid}`,
                //       newType: 10,
                //     })
                //     .then((res) => {});
                // }
                // setTimeout(() => {
                //   uni.hideLoading();
                //   let merchandise_name = uni.getStorageSync("merchandise_name");
                //   uni.navigateTo({
                //     url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`,
                //   });
                // }, 1500);
                _self.wechatPayListener(
                  res.data.order_number,
                  _self.info.price,
                  url
                );
              }

              //状态
              return;
              /* eslint-enable */
              // setTimeout
            },
            cancel: function(res) {
              uni.hideLoading();
              uni.showToast({
                title: "支付失败",
                duration: 1000,
                icon: "none",
              });
              /* eslint-disable */
              // _self.$refs.confirm_dialog.open();
              /* eslint-enable */
            },
            fail: function(res) {
              /* eslint-disable */
              // console.log(res);
              /* eslint-enable */
            },
          });
        });
      } else {
      }
    },
    wechatPayListener(order_number, price, url) {
      uni.showLoading({
        title: "加载中...",
      });
      let time = 0;
      let t = setInterval(() => {
        time += 1;
        if (time == 6) {
          clearInterval(t);
        }
        userApi.GetPayResult({ order_number }).then((res) => {
          if (res.data.pay_status == "is_pay") {
            let callWechat = uni.getStorageSync("callWechat");

            if (!callWechat) {
              uni.setStorageSync("callWechat", true);
              //腾讯广告引流付费统计
              gdt("track", "COMPLETE_ORDER", { value: price });
              let bdVid = uni.getStorageSync("bdVid");
              if (bdVid) {
                userApi
                  .toBaidu({ logidUrl: `${url}&bd_vid=${bdVid}`, newType: 10 })
                  .then((res) => {});
              }
              // let trackId = uni.getStorageSync("trackId");
              // if (trackId) {
              //   userApi.toRed({ trackId, type: 3 }).then((res) => {});
              // }
            }
            uni.hideLoading();
            let merchandise_name = uni.getStorageSync("merchandise_name");
            clearInterval(t);
            uni.navigateTo({
              url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`,
            });
          }
        });
      }, 1000);
    },
    isPhoneExp(str) {
      let pattern = /^1(3|4|5|6|7|8|9)\d{9}$/;
      return pattern.test(str);
    },
    goOrder() {
      if (this.mobile && this.sms_code) {
        if (this.info.price !== 0) {
          this.$refs["popup"].open();
        } else {
          this.reallyPay();
        }
      } else {
        uni.showToast({
          title: "信息不能为空，请正确填写。",
          duration: 2000,
          icon: "none",
        });
      }
    },
    closePopop() {
      this.$refs.popup.close();
    },
    radioChange(check) {
      this.pay_channel = check.detail.value;
    },
    send_sms() {
      if (
        (this.button_text === "获取验证码" ||
          this.button_text === "重新获取") &&
        this.mobile
      ) {
        if (this.isPhoneExp(this.mobile)) {
          userApi.GetSendSMS({ mobile: this.mobile }).then((res) => {
            // console.log(res);
            if (res.statusCode == 200) {
              this.time = 60;
              this.time_down();
              this.code_blur = false;
            }
          });
        } else {
          uni.showToast({
            title: "请输入正确的手机号",
            duration: 1000,
            icon: "none",
          });
        }
      } else if (!this.mobile) {
        uni.showToast({
          title: "请输入手机号",
          duration: 1000,
          icon: "none",
        });
      }
    },
    time_down() {
      if (this.time > 0) {
        this.time--;
        this.button_text = this.time + "s后重发";
        setTimeout(this.time_down, 1000);
        this.isDisabled = true;
      } else {
        this.time = 0;
        this.button_text = "重新获取";
        this.code_blur = true;
        this.isDisabled = false;
      }
    },
    go_private() {
      uni.navigateTo({
        url: `/pages/privateAgreement/privateAgreement`,
      });
    },
    reallyPay() {
      const { id } = this.info;
      if (id == 0) {
        uni.showToast({
          title: "课程id不存在,请联系客服",
          duration: 2000,
          icon: "none",
        });
        return;
      }
      if (id == "172") {
        //添加自定义属性，如:点击“开始阅读”按钮
        hina.track("classes_pay_click", {
          classes_type: "聂道少儿围棋",
        });
        console.log("hina :>> ", hina);
      }
      if (this.mobile && this.sms_code) {
        uni.setStorageSync(
          "start_time",
          this.info.fixed_time
            ? this.moment(this.info.fixed_time).format("YYYY-MM-DD HH:mm:ss")
            : ""
        );
        let clickid = uni.getStorageSync("clickid");
        if (clickid) {
          userApi
            .courseClick({
              click_id: clickid,
              event_type: "shopping",
              isShowToast: false,
            })
            .then((res) => {});
        }
        let bdVid = uni.getStorageSync("bdVid");
        if (bdVid) {
          let url = window.location.href;
          userApi
            .toBaidu({ logidUrl: `${url}&bd_vid=${bdVid}`, newType: 3 })
            .then((res) => {});
        }
        if (
          /micromessenger/.test(navigator.userAgent.toLowerCase()) &&
          this.info.price > 0
        ) {
          // 拉起支付

          this.h5_wechat_pay();
        } else {
          uni.showLoading({
            title: "加载中",
          });
          const source_params = this.get_adv_source();
          let trackId = uni.getStorageSync("trackId") || "";
          let from = {
            mobile: this.mobile,
            sms_code: this.sms_code,
            source: this.options.source ? this.options.source : "",
            course_id: this.info.id,
            tw_callback: uni.getStorageSync("tw_callback"),
            click_id: clickid,
            pay_channel: this.pay_channel,
            teacher_id: this.options?.teacher_id || "",
            ali_callback:
              this.pay_channel === "aliPay"
                ? window.location.href + "&from=aliPay&order_number="
                : "",
            po_id: this.options.po_id ? this.options.po_id : "",
            ...source_params,
            trackId,
            recommend_id: this.options.recommend_id || "",
          };
          userApi.CreateOrder(from).then((res) => {
            uni.hideLoading();
            if (res.statusCode == 200) {
              if (this.info.price == 0) {
                uni.navigateTo({
                  url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${this.info.merchandise_name}`,
                });
              } else {
                if (this.pay_channel === "aliPay") {
                  // let pay_url = res.data.pay_url;
                  // if (pay_url) {
                  //   uni.hideLoading();
                  //   window.location.href = pay_url;
                  // }
                  if (res.data) {
                    // uni.showLoading({
                    //   title: "订单支付中..",
                    // });
                    this.payListener(res.data.order_number);
                    const merchandise_name = this.info.merchandise_name;
                    const origin = window.location.origin;
                    window.location.href = `alipays://platformapi/startapp?saId=10000007&qrcode=${origin}/pages/order/alipay?pay_url=${res.data.pay_url}_order_number_${res.data.order_number}_merchandise_name_${merchandise_name}`;
                  }
                } else {
                  uni.hideLoading();
                  window.location.replace(
                    res.data.mweb_url +
                      "&redirect_url=" +
                      encodeURIComponent(
                        window.location.href +
                          "&from=wechat&order_number=" +
                          res.data.order_number
                      )
                  );
                }
              }
            }
          });
        }
      } else {
        uni.showToast({
          title: "信息不能为空，请正确填写。",
          duration: 2000,
          icon: "none",
        });
      }
    },
    payListener(order_number) {
      if (this.payListenerTimer) {
        clearInterval(this.payListenerTimer);
      }
      let payListenerTimer = setInterval(() => {
        userApi.GetPayResult({ order_number }).then((res) => {
          if (res.data.pay_status !== "in_pay") {
            clearInterval(payListenerTimer);
            uni.hideLoading();
          } else {
            uni.showLoading({
              title: "订单支付中..",
            });
          }
          if (res.data.pay_status == "no_pay") {
            uni.showToast({
              title: "支付失败",
              duration: 1000,
              icon: "none",
            });
          }
          if (res.data.pay_status == "is_pay") {
            uni.setStorageSync(
              "start_time",
              this.info.fixed_time
                ? this.moment(this.info.fixed_time).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                : ""
            );
            let bdVid = uni.getStorageSync("bdVid");
            if (bdVid) {
              let url = window.location.href;
              userApi
                .toBaidu({ logidUrl: `${url}&bd_vid=${bdVid}`, newType: 10 })
                .then((res) => {});
            }
            // let trackId = uni.getStorageSync("trackId");
            // if (trackId) {
            //   userApi.toRed({ trackId, type: 3 }).then((res) => {});
            // }
            //腾讯广告引流付费统计
            gdt("track", "COMPLETE_ORDER", { value: this.info.price });
            uni.showToast({
              title: "支付成功",
              duration: 1000,
              icon: "success",
            });
            uni.navigateTo({
              url: `/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${this.info.merchandise_name}`,
            });
          }
          // }
        });
      }, 1000);
    },
    initClick(clickid, qr_code) {
      userApi
        .courseClick({ click_id: clickid, event_type: "active_pay" })
        .then((res) => {
          console.log(res);
          uni.navigateTo({
            url: `/pages/success/success?qr_code=${qr_code}&merchandise_name=${this.info.merchandise_name}`,
          });
        });
    },
  },
};
</script>

<style>
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}

/* 解决H5 的问题 */
uni-scroll-view .uni-scroll-view::-webkit-scrollbar {
  /* 隐藏滚动条，但依旧具备可以滚动的功能 */
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
.content {
  position: relative;
  overflow: hidden;
  height: 100vh;
  /* background: #ff3e1e; */
  /* display: flex;
  flex-direction: column; */
}
.img-container {
  overflow-y: scroll;
  height: 100vh;
  width: 100vw;
  /* padding-bottom: 330rpx; */
  background: #ff3e1e;
  /* box-sizing: border-box; */
}
.logo {
  width: 100vw;
  /* margin-bottom: 330rpx; */
}
.price {
  font-size: 56rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
.blank-container {
  width: 100vw;
  height: 330rpx;
  background: #ff3e1e;
}
.bottom_shop {
  width: 100vw;
  height: 330rpx;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  box-shadow: 0 2rpx 30rpx 0 rgba(45, 45, 45, 0.2);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  padding: 25rpx 0 30rpx 0;
  box-sizing: border-box;
  border-radius: 50rpx 50rpx 0 0;
}
.input-container {
  /* width: 100%;
  margin: 0 50rpx; */
  /* box-sizing: border-box; */
}
.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22rpx 40rpx;
  font-size: 36rpx;
  color: #7b7b7b;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 2rpx solid #f1f1f1;
}
.mobile-input-container {
  margin-bottom: 20rpx;
}
.uni_input {
  display: flex;
  align-items: center;
  width: 646rpx;
  height: 80rpx;
  border: 1rpx solid #ff6c00;
  border-radius: 25rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
  padding-right: 11rpx;
}
.select-payment-popop {
  background: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 540rpx;
  box-sizing: border-box;
  border-radius: 50rpx 50rpx 0 0;
  justify-content: space-between;
}
.mobile-inut {
  width: 100%;
  height: 100%;
  font-size: 34rpx;
}
.code_input {
  width: 80%;
  height: 100%;
  font-size: 34rpx;
}
.out_input {
  width: 400rpx;
}
.label {
  width: 180rpx;
  font-size: 30rpx;
}
.line {
  background: #f1f1f1;
  height: 2rpx;
  width: 100%;
  margin: 20rpx 0;
}
.code_btn {
  width: 220rpx;
  height: 62rpx;
  border-radius: 20rpx;
  background: #ff3e1e;
  font-size: 30rpx;
  text-align: center;
  line-height: 62rpx;
  color: #fff;
}
.disable-button {
  background: #cacdd2;
}
.shop_btn {
  width: 514rpx;
  height: 88rpx;
  border-radius: 44rpx;
  background: #ff3e1e;
  text-align: center;
  line-height: 88rpx;
  color: #fff;
  font-size: 34rpx;
  box-shadow: 0 6rpx 30rpx 0 rgba(255, 102, 68, 0.5);
  animation-name: scaleDraw; /*关键帧名称*/
  animation-timing-function: ease-in-out; /*动画的速度曲线*/
  animation-iteration-count: infinite; /*动画播放的次数*/
  animation-duration: 2s; /*动画所花费的时间*/
}
.goPay-button {
  width: 400rpx;
  height: 70rpx;
  line-height: 70rpx;
}
.close-icon {
  width: 26rpx;
  height: 26rpx;
}
.pay-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  background: #fff;
  margin-bottom: 32rpx;
}
.pay-label-container {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: flex-end;
}
.pay-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 16rpx;
}
.pay-label-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  align-items: flex-start;
}
.pay-label {
  font-size: 32rpx;
  line-height: 40rpx;
  font-weight: bold;
  color: #333333;
}
.pay-issue {
  color: #abadb0;
  font-size: 24rpx;
  line-height: 32rpx;
}
.confirm-pay-button {
  margin-bottom: 50rpx;
}
.pay_container {
  width: 100vw;
  padding: 0 73rpx;
  box-sizing: border-box;
  margin-top: 30rpx;
}
</style>
