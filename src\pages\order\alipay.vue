<template>
  <view></view>
</template>

<script>
import userApi from "../../api/user";
export default {
  data() {
    return {
      pay_url: "",
      order_number: "",
      payTimer: null,
      merchandise_name: "",
    };
  },
  onLoad(option) {
    const { pay_url } = option;
    const str_arr = pay_url.split("_order_number_");
    this.merchandise_name = pay_url.split("_merchandise_name_")[1];
    this.pay_url = str_arr[0];
    this.order_number = pay_url
      .split("_merchandise_name_")[0]
      .split("_order_number_")[1];
    if (this.order_number) {
      this.payListener(this.order_number);
    }
  },
  created() {
    const pay_url = this.pay_url;
    this.alipay_ready(() => {
      if (pay_url) {
        AlipayJSBridge.call("pushWindow", {
          url: pay_url,
          param: {
            readTitle: true,
            showOptionMenu: false,
          },
        });
      }

      // alert(pay_url);
      // AlipayJSBridge.call(
      //   "tradePay",
      //   {
      //     tradeNO: pay_url,
      //   },
      //   function (result) {
      //     alert(JSON.stringify(result));
      //   }
      // );
    });
  },
  methods: {
    alipay_ready(callback) {
      // 如果jsbridge已经注入则直接调用
      if (window.AlipayJSBridge) {
        callback && callback();
      } else {
        // 如果没有注入则监听注入的事件
        document.addEventListener("AlipayJSBridgeReady", callback, false);
      }
    },
    payListener(order_number) {
      if (this.payTimer !== null) {
        clearInterval(this.payTimer);
      }
      this.payTimer = setInterval(() => {
        userApi
          .GetPayResult({ order_number })
          .then((res) => {
            if (res.data.pay_status != "in_pay") {
              clearInterval(this.payTimer);
            } else {
              uni.hideLoading();
            }
            if (res.data.pay_status == "no_pay") {
              uni.showToast({
                title: "支付失败",
                duration: 3000,
                icon: "none",
              });
            }
            if (res.data.pay_status == "is_pay") {
              uni.showToast({
                title: "支付成功",
                duration: 3000,
                icon: "success",
              });
              const origin = window.location.origin;
              const merchandise_name = encodeURIComponent(
                this.merchandise_name
              );
              const open_url = `${origin}/pages/success/success?qr_code=${res.data.qr_code}&merchandise_name=${merchandise_name}`;
              AlipayJSBridge.call("pushWindow", {
                url: open_url,
                param: {
                  readTitle: true,
                  showOptionMenu: false,
                },
              });
            }
          })
          .catch(() => {
            clearInterval(this.payTimer);
            alert("获取支付结果失败");
          });
      }, 2000);
    },
  },
};
</script>

<style lang="scss" scoped></style>
