#FROM node:14.18.2-buster AS build-dist
FROM node:16.15.1-buster AS build-dist

#FROM node:20 AS build-dist
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list

RUN apt-get update && apt-get install python2 -y

ADD ./ /workspace

WORKDIR workspace

RUN npm install --registry=http://registry.npmmirror.com

#RUN npm config set registry http://registry.npmmirror.com

RUN npm rebuild node-sass

#RUN npx browserslist@latest --update-db
#RUN npx update-browserslist-db@latest
#RUN npm install -g npm@latest
#RUN npm install -g browserslist@latest


RUN npm run build:h5 --debug
RUN ls -al /workspace



FROM nginx:stable-alpine
LABEL maintainer="eStarGo <<EMAIL>>"
MAINTAINER eStarGo <<EMAIL>>
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache -U tzdata
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai  /etc/localtime
COPY --from=build-dist /workspace/dist/build/h5 /etc/nginx/html
COPY --from=build-dist /workspace/nginx.conf /etc/nginx/nginx.conf
COPY --from=build-dist /workspace/mp/MP_verify_yHXAdTvi4lIN0U1K.txt /etc/nginx/mp/MP_verify_yHXAdTvi4lIN0U1K.txt

