/* 分界线 */
.uni-page-head-transparent {
  background-color: transparent !important;
}
.uni-divider {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.uni-page-head-hd > .uni-page-head-btn {
  background-color: rgba(0, 0, 0, 0.3) !important;
  text-align: center !important;
  display: flex !important;
  -webkit-box-align: center !important;
  -webkit-align-items: center !important;
  align-items: center !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
}
.uni-btn-icon {
  color: #fff !important;
  font-weight: 600;
  font-size: 48rpx !important;
  text-align: center !important;
  margin-left: 4rpx;
}
.uni-divider__content {
  font-size: 28rpx;
  /* color: #999; */
  padding: 0 20rpx;
  position: relative;
  z-index: 101;
  background: #f4f5f6;
}
.uni-divider__line {
  background-color: #cccccc;
  height: 1px;
  width: 100%;
  position: absolute;
  z-index: 100;
  top: 50%;
  left: 0;
  transform: translateY(50%);
}
.uni-button--danger {
  padding-left: 4px;
  padding-right: 4px;
  border: none;
  background-color: #f56c6c;
  color: #fff;
}
.uni-button--success {
  padding-left: 4px;
  padding-right: 4px;
  border: none;
  background-color: #67c23a;
  color: #fff;
}
.uni-button--info {
  padding-left: 4px;
  padding-right: 4px;
  background-color: rgb(202, 205, 210);
  border-color: rgb(202, 205, 210);
  color: #fff;
}
.uni-button--gray {
  padding-left: 4px;
  padding-right: 4px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  color: #606266;
}
.uni-button--disabled {
  padding-left: 4px;
  padding-right: 4px;
  border: 1px solid #cacdd2;
  background-color: #cacdd2;
  color: #fff;
}
.uni-button--gradient-blue {
  background-image: linear-gradient(179deg, #e2f9ff 0%, #c0eeff 98%);
  box-shadow: 0 6rpx 0 0 #16c5ff;
  border-radius: 40rpx;
  font-size: 34rpx;
  color: #005e87;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;
}
.uni-sample-toast .uni-simple-toast__text {
  border-radius: 48rpx;
}
/* 按钮样式 */
/* 1.按钮尺寸 */
.bigButton-size {
  /* width: 556rpx; */
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;
  margin: 0 auto;
  font-size: 34rpx;
  text-align: center;
  font-weight: 600;
  margin: 0 40rpx;
}
.middleButton-size {
  width: 335rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin: 0 auto;
  font-size: 34rpx;
  text-align: center;
  font-weight: 600;
}
.middleButton1-size {
  width: 303rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin: 0 auto;
  font-size: 34rpx;
  text-align: center;
  font-weight: 600;
}
.smallButton-size {
  width: 238rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin: 0 auto;
  font-size: 34rpx;
  text-align: center;
  font-weight: 600;
}
.superSmallButton-size {
  width: 132rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 32rpx;
  margin: 0 auto;
  font-size: 34rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}
/* 2.按钮颜色和阴影及内部字体颜色 */
/* 蓝色 */
.button-blue {
  background: #31bfff;
  color: #fff;
}
/* 红色 */
.button-red {
  background-image: linear-gradient(90deg, #ff595a 0%, #ff4d4d 100%);
  box-shadow: 0 6rpx 20rpx 0 rgba(255, 77, 77, 0.35);
  color: #ffffff;
}
/* 蓝色渐变 */
.blue-gradient {
  background-image: linear-gradient(179deg, #e2f9ff 0%, #c0eeff 98%);
  box-shadow: inset 0 -6rpx 0 0 #16c5ff;
  color: #005e87;
}
/* 禁止点击 */
.buttton-disable {
  background-image: linear-gradient(179deg, #e2e6ec 0%, #c3c7cd 98%);
  box-shadow: inset 0 -6rpx 0 0 #acb1b9;
  color: #ffffff;
  pointer-events: none;
}
/* 白色背景红色文字 */
.buttton-white {
  background-color: #fff;
  color: #fe3b30;
}
/* 白色背景蓝色文字 */
.buttton-white-blue {
  background-color: #fff;
  color: #31bfff;
}
/* 橘色背景白色字 */
.button-orange {
  background-color: rgb(255, 175, 30);
  color: #fff;
}
/* 按钮阴影 */
.blue-shaow {
  box-shadow: 0 6rpx 20rpx 0 rgba(49, 191, 255, 0.25);
}
/* 字体颜色 */
.h1Color {
}
.contentColor {
}
/* 方块及阴影 */
.square {
  margin: 32rpx 32rpx 0;
  padding: 40rpx;
  box-shadow: 0 6rpx 30rpx 0 rgba(45, 45, 45, 0.1);
  border-radius: 35rpx;
  box-sizing: border-box;
  background: #ffffff;
}

/* // ios底部安全距离-padding */
.savepadding {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: content-box;
}
