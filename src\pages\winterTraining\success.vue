<template>
  <view class="success-page">
    <view class="success-content">
      <view class="icon-wrap">
        <image class="banner-img" src="../../static/winterSucc.png" />
      </view>
      <view class="main-content">
        <view class="info-wrap">
          <view class="info-title">报名信息</view>
          <view class="info-item">
            <text class="info-label">姓名</text>
            <text class="info-value">{{ formData.name }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">手机号</text>
            <text class="info-value">{{ formData.mobile }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">日期</text>
            <text class="info-value">{{ formData.date }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">时间</text>
            <text class="info-value">{{ formData.time }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">授课讲师</text>
            <text class="info-value">{{ formData.teacherName }}</text>
          </view>
        </view>

        <view class="text-wrap">
          <view class="success-text">
            家长您好，集训课选期成功！
          </view>
          <view class="success-tip">
            记得将截图发送给集训班主任老师哦~
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: "",
        mobile: "",
        date: "",
        time: "",
        teacherIndex: -1,
      },
    };
  },

  onLoad(options) {
    const formData = uni.getStorageSync("winterTrainingForm");
    if (formData) {
      this.formData = JSON.parse(formData);
    }
  },
};
</script>

<style lang="scss" scoped>
.success-page {
  min-height: 100vh;
  background-color: #f7f9fc;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .success-content {
    text-align: center;
    padding: 0 30rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .icon-wrap {
    margin-bottom: 40rpx;
    padding-top: 40rpx;

    .banner-img {
      width: 180rpx;
      height: 180rpx;
    }
  }

  .main-content {
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin: 0 auto;
    width: 100%;
    max-width: 690rpx;
  }

  .info-wrap {
    padding: 40rpx;
    background: #fff;

    .info-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 30rpx;
      text-align: left;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 24rpx;
        background: #ff6644;
        border-radius: 3rpx;
      }
    }

    .info-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      &:first-child {
        padding-top: 0;
      }
    }

    .info-label {
      width: 160rpx;
      text-align: left;
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      flex: 1;
      text-align: right;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .text-wrap {
    padding: 30rpx;
    background: #f8f9fc;
    border-top: 1rpx solid #f0f0f0;

    .success-text {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 24rpx;
      line-height: 1.4;
    }

    .success-tip {
      font-size: 28rpx;
      color: #666;
      line-height: 1.4;
      position: relative;
      display: inline-block;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        bottom: -2rpx;
        height: 8rpx;
        background: rgba(255, 102, 68, 0.1);
        z-index: 1;
      }
    }
  }
}
</style>
