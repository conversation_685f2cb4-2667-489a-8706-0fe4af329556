<template>
  <view class="container">
    <view class="page">
      <img src="../../static/gamebg.png" class="bg" />
      <view class="flex-wrap">
        <view class="items-center text-wrapper"
          ><text class="text_3"
            >用时：{{ mm < 10 ? '0' + mm : mm }}:{{
              ss < 10 ? '0' + ss : ss
            }}</text
          >
        </view>
      </view>
      <img src="../../static/vs.png" class="vs" />
      <view class="flex-wrap">
        <view class="board-wrap">
          <gameBoard
            ref="player_board"
            id="board"
            :boardClick="board_click"
            :boardObj="board_obj"
            :turnOnAudio="true"
            :backGroundColor="'#ffd6a1'"
            @chessMove="chess_move"
            @turn="change_turn"
            @captured="change_captured"
          ></gameBoard>
        </view>
      </view>
      <view
        class="cover_bg"
        v-if="game_status === 'win' || game_status === 'lose'"
      ></view>
      <view class="group_7" v-if="game_status === 'win'">
        <img src="../../static/game_win.png" class="dialog_bg" />
        <view class="section_19 flex-wrap cap_left"
          ><text class="font_3 text_7">提{{ black_captured }}子</text>
        </view>
        <view class="section_19 flex-wrap cap_right"
          ><text class="font_3 text_7">提{{ white_captured }}子</text>
        </view>
        <view
          class="flex-wrap items-center text-wrapper_2 report_btn"
          @click="go_report"
          ><text class="text_9">查看报告</text>
        </view>
      </view>
      <view class="group_7" v-if="game_status === 'lose'">
        <img src="../../static/game_lose.png" class="dialog_bg" />
        <view class="section_19 flex-wrap cap_left"
          ><text class="font_3 text_7">提{{ black_captured }}子</text>
        </view>
        <view class="section_19 flex-wrap cap_right"
          ><text class="font_3 text_7">提{{ white_captured }}子</text>
        </view>
        <view
          class="flex-wrap items-center text-wrapper_2 report_btn"
          @click="go_report"
          ><text class="text_9">查看报告</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import gameBoard from "@/plugins/board/gameBoard";
import matchApi from "../../api/elaluation";
export default {
  data() {
    return {
      qr_code: "",
      start_time: "",
      board_click: false,
      board_obj: {
        sgf: "(;CA[gb1312]SZ[3])",
        section: {
          left: 0,
          top: 0,
          right: 0,
          bottom: 0
        },
        board_model: "normal"
      },
      black_captured: 0,
      white_captured: 0,
      turn: "B",
      game_status: "start",
      mm: 0,
      ss: 0,
      timer: null,
      match_id: "",
    };
  },
  components: {
    gameBoard,
  },
  onLoad(option) {
    this.mm = option.mm || 0
    this.ss = option.ss || 0
    this.mm = parseInt(this.mm)
    this.ss = parseInt(this.ss)
    this.match_id = option.match_id || ""
    this.board_click = true;
    this.game_status = "start";
    this.timeChange();
  },

  methods: {
    chess_move: function (ob) {
      if (!this.board_click && this.game_status === "start") {
        return;
      }
      this.$refs.player_board.chess_move({
        x: ob.x,
        y: ob.y,
        c: ob.c
      });
      this.board_click = false;
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.game_status === "start") {
            this.randomMove();
          }
        }, 500);
      })
    },

    randomMove(pList = [0, 1, 2, 3, 4, 5, 6, 7, 8]) {
      if (!pList.length) {
        this.win_game()
        return
      }
      let posIndex = parseInt(pList.length * Math.random())
      let pos = pList[posIndex]
      let rx = parseInt(pos / 3)
      let ry = pos % 3
      let err = this.$refs.player_board.chess_move({
        x: rx,
        y: ry,
        c: this.turn
      });
      if (err) {
        pList.splice(posIndex, 1)
        this.randomMove(pList)
      } else {
        if (this.game_status === "start") {
          this.board_click = true;

        }
      }
    },
    win_game() {
      console.log("win!")
      this.game_status = "win";
      this.sendResult(1);
    },
    lose_game() {
      console.log("lose!")
      this.game_status = "lose";
      this.sendResult(0);
    },
    sendResult(win_count) {
      this.board_click = false;
      clearInterval(this.timer);
      uni.setStorageSync("game_result", 1);
      if (this.match_id && this.match_id !== "undefined") {
        let qList = uni.getStorageSync("question_result")
        var right_count = 0;
        for (const item of qList || []) {
          if (item === "Y") {
            right_count++
          }
        }
        var star = 3;
        if (right_count >= 16) {
          star = 5;
        } else if (right_count < 16 && right_count >= 10) {
          star = 4;
        } else {
          star = 3;
        }
        matchApi.MacthResult({
          "match_id": parseInt(this.match_id),
          "question_right": right_count || 0, // 答对题目数量
          "game_win": win_count || 0, // 赢的对局数量
          "star": star // 评级星数
        }).then((res) => {
          console.log(res)
        })
      }

    },
    change_turn(e) {
      this.turn = e;
    },
    change_captured: function (event) {
      this.black_captured = event.B;
      this.white_captured = event.W;
      if (this.black_captured >= 1) {
        this.win_game()
      }
      if (this.white_captured >= 1) {
        this.lose_game()
      }
    },

    go_report() {
      console.log("go_report")
      // TODO report-router
      uni.navigateTo({
        url: `/pages/report/report?id=${this.match_id}&mm=${this.mm}&ss=${this.ss}`
      });
    },
    timeChange() {
      this.timer = setInterval(() => {
        if (this.ss == 59) {
          this.mm += 1;
          this.ss = 0;
        } else {
          this.ss += 1;
        }
      }, 1000);
    },
  },
}
</script>

<style scoped>
.tab {
  width: 100vw;
  height: 10vh;
}
.page {
  width: 100vw;
  height: 100vh;
}
.bg {
  position: absolute;
  width: 100vw;
  height: 100vh;
  z-index: -1;
}
.vs {
  margin: 30rpx 5vw;
  width: 90vw;
}
.flex-wrap {
  display: flex;
  justify-content: center;
}
.text-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 100rpx;
  padding: 7rpx 0 6rpx;
  align-self: center;
  background-color: #00000026;
  border-radius: 27.5rpx;
  width: 248rpx;
}
.text_3 {
  color: #ffffff;
  font-size: 30rpx;
  font-family: 'PingFang SC';
  font-weight: 500;
  line-height: 42rpx;
}
.board-wrap {
  width: 650rpx;
  height: 650rpx;
  /* margin-top: 50rpx; */
  background: url('../../static/board-bg.png') no-repeat;
  background-size: cover;
  box-shadow: 0 8rpx 0 0 #c79c72;
}

.font_1 {
  font-size: 36rpx;
  font-family: 'PingFang SC';
  line-height: 50rpx;
  font-weight: 500;
  color: #333333;
}
.group_7 {
  filter: drop-shadow(0px 7rpx 18rpx #38383833);
  position: absolute;
  top: 200rpx;
  left: 25rpx;
  z-index: 10;
}
.dialog_bg {
  width: 700rpx;
  position: absolute;
  z-index: -1;
}
.section_19 {
  background-color: #e7f7ff;
  border-radius: 30rpx;
  width: 205rpx;
  height: 60rpx;
}
.font_3 {
  font-size: 32rpx;
  font-family: 'PingFang SC';
  line-height: 60rpx;
  font-weight: 500;
  color: #31bfff;
}
.cap_left {
  position: absolute;
  top: 560rpx;
  left: 80rpx;
}
.cap_right {
  position: absolute;
  top: 560rpx;
  left: 420rpx;
}
.report_btn {
  position: absolute;
  top: 660rpx;
  left: 215rpx;
}
.text-wrapper_2 {
  padding: 19rpx 0 21rpx;
  align-self: center;
  background-color: #31bfff;
  border-radius: 44rpx;
  width: 280rpx;
}
.text_9 {
  color: #ffffff;
  font-size: 34rpx;
  font-family: 'PingFang SC';
  font-weight: 500;
  line-height: 48rpx;
}
.cover_bg {
  position: absolute;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  top: 0;
  left: 0;
  z-index: 2;
}
</style>