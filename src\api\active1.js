import axios from "../http";

function getEventList(data) {
  return axios
    .get("/api/public/event/list", data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function createOrder(data) {
  return axios
    .post("/api/public/event/create", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getEventLevel(data) {
  return axios
    .get("/api/public/event/level", data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getEventConfig(data) {
  return axios
    .get("/api/public/event/config", data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getPayResult(data) {
  return axios
    .get("/api/public/event/result", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
function getSendSMS(data) {
  return axios
    .post("/api/public/user/sendSMS", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    });
}
export default {
  async GetEventList(data) {
    return getEventList(data);
  },
  async GetEventConfig(data) {
    return getEventConfig(data);
  },
  async GetSendSMS(data) {
    return getSendSMS(data);
  },
  async GetEventLevel(data) {
    return getEventLevel(data);
  },
  async CreateOrder(data) {
    return createOrder(data);
  },
  async GetPayResult(data) {
    return getPayResult(data);
  },
};
